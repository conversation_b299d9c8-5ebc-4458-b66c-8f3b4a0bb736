<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Montfort Group - Global Commodity Trading</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Base styles */
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            overflow-x: hidden;
            background-color: #000;
            scroll-behavior: smooth;
        }

        /* Fixed Scene Container for the background animation */
        .scene-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            z-index: -1;
            border-radius: 0;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        /* Background Gradient (Controlled by JS scroll) */
        .background-gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 400vh;
            background: linear-gradient(
                to bottom,
                #0a0a2a 0%,    /* Deep Night Blue */
                #1a1a3a 15%,   /* Darker Purple */
                #3a0a5a 30%,   /* Mid Purple */
                #6a0a6a 45%,   /* Bright Purple */
                #ff6f61 60%,   /* Fiery Orange */
                #ffd700 75%,   /* Warm Yellow */
                #87ceeb 90%,   /* Sky Blue */
                #add8e6 100%   /* Light Blue */
            );
            transform: translateY(0);
            transition: background 0.5s ease-out;
        }

        /* Sun & Moon */
        .celestial-body {
            position: absolute;
            border-radius: 50%;
            transition: opacity 1s ease-in-out, transform 1s ease-in-out, background-color 1s ease-in-out;
            z-index: 25;
        }

        .sun {
            width: 100px;
            height: 100px;
            background-color: #ffd700;
            box-shadow: 0 0 40px 15px rgba(255, 215, 0, 0.7);
            top: 120%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
        }

        .moon {
            width: 80px;
            height: 80px;
            background-color: #f0f0f0;
            box-shadow: 0 0 30px 10px rgba(240, 240, 240, 0.5);
            top: -20%;
            left: 60%;
            transform: translateX(-50%);
            opacity: 0;
        }

        /* Mountain Layers (Parallax, Controlled by JS scroll) */
        .mountain-layer {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            transition: background-color 0.5s ease-out;
        }

        /* Distant Mountains - more jagged */
        .mountains-distant {
            z-index: 30;
            background-color: rgba(10, 10, 42, 0.8);
            clip-path: polygon(
                0% 100%, 0% 75%, 15% 60%, 30% 78%, 45% 55%, 60% 85%, 75% 68%, 90% 82%, 100% 65%, 100% 100%
            );
            transform: translateX(0px);
        }

        /* Mid Mountains - sharper peaks */
        .mountains-mid {
            z-index: 40;
            background-color: rgba(10, 10, 42, 0.9);
            clip-path: polygon(
                0% 100%, 0% 85%, 10% 70%, 25% 90%, 40% 65%, 55% 95%, 70% 75%, 85% 92%, 100% 80%, 100% 100%
            );
            transform: translateX(0px);
        }

        /* Foreground Mountains - bold and close */
        .mountains-foreground {
            z-index: 50;
            background-color: rgba(10, 10, 42, 1);
            clip-path: polygon(
                0% 100%, 0% 95%, 12% 80%, 28% 98%, 40% 75%, 55% 100%, 70% 80%, 88% 95%, 100% 85%, 100% 100%
            );
            transform: translateX(0px);
        }

        /* Clouds/Mist */
        .clouds-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 35;
            pointer-events: none;
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }

        .cloud {
            position: absolute;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 50%;
            filter: blur(8px);
            animation: cloud-drift linear infinite;
            opacity: 1;
        }

        .cloud:nth-child(1) { width: 150px; height: 80px; top: 10%; left: -20%; animation-duration: 60s; animation-delay: 0s; }
        .cloud:nth-child(2) { width: 180px; height: 90px; top: 25%; left: -30%; animation-duration: 75s; animation-delay: 15s; }
        .cloud:nth-child(3) { width: 120px; height: 70px; top: 18%; left: -10%; animation-duration: 50s; animation-delay: 30s; }
        .cloud:nth-child(4) { width: 200px; height: 100px; top: 35%; left: -40%; animation-duration: 90s; animation-delay: 45s; }

        @keyframes cloud-drift {
            0% { transform: translateX(0%); }
            100% { transform: translateX(120vw); }
        }

        /* Trees/Grass Layer */
        .trees-grass-layer {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 30%;
            z-index: 60;
            pointer-events: none;
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }

        .tree-cluster {
            position: absolute;
            bottom: 0;
            transition: filter 1s ease-in-out, transform 0.5s ease-in-out;
        }

        .tree-cluster.type-1 {
            left: 10%;
            width: 60px;
            height: 100px;
        }
        .tree-cluster.type-1::before, .tree-cluster.type-1::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 50% 50% 0 0;
            background-color: var(--tree-color, #228B22);
            transition: background-color 1s ease-in-out;
        }
        .tree-cluster.type-1::before {
            width: 60px;
            height: 80px;
            bottom: 20px;
            transform: translateX(-50%) rotate(var(--sway-angle, -5deg));
            animation: sway 5s ease-in-out infinite alternate;
        }
        .tree-cluster.type-1::after {
            width: 50px;
            height: 70px;
            bottom: 0;
            transform: translateX(-50%) rotate(var(--sway-angle-rev, 5deg));
            animation: sway 4.5s ease-in-out infinite alternate-reverse;
        }

        .tree-cluster.type-2 {
            right: 15%;
            width: 50px;
            height: 80px;
        }
        .tree-cluster.type-2::before, .tree-cluster.type-2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 50% 50% 0 0;
            background-color: var(--tree-color, #228B22);
            transition: background-color 1s ease-in-out;
        }
        .tree-cluster.type-2::before {
            width: 50px;
            height: 70px;
            bottom: 15px;
            transform: translateX(-50%) rotate(var(--sway-angle, 3deg));
            animation: sway 4s ease-in-out infinite alternate;
        }
        .tree-cluster.type-2::after {
            width: 40px;
            height: 60px;
            bottom: 0;
            transform: translateX(-50%) rotate(var(--sway-angle-rev, -3deg));
            animation: sway 3.8s ease-in-out infinite alternate-reverse;
        }

        @keyframes sway {
            0% { transform: rotate(-5deg); }
            100% { transform: rotate(5deg); }
        }

        /* Night Elements Layer */
        .night-elements-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 20;
            pointer-events: none;
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }

        .star, .firefly {
            position: absolute;
            border-radius: 50%;
        }

        .star {
            background-color: white;
            width: 2px;
            height: 2px;
            animation: twinkle linear infinite alternate;
            animation-duration: var(--star-duration);
            animation-delay: var(--star-delay);
        }

        .firefly {
            width: 4px;
            height: 4px;
            background-color: #ffcc00;
            box-shadow: 0 0 5px 2px rgba(255, 204, 0, 0.7);
            animation: pulse-and-move linear infinite;
            animation-duration: var(--firefly-duration);
            animation-delay: var(--firefly-delay);
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0; }
            50% { opacity: 1; }
        }

        @keyframes pulse-and-move {
            0% { transform: translate(0, 0); opacity: 0; }
            25% { opacity: 0.8; transform: translate(10px, -5px); }
            50% { opacity: 0.5; transform: translate(20px, 10px); }
            75% { opacity: 0.8; transform: translate(30px, -15px); }
            100% { transform: translate(40px, 0); opacity: 0; }
        }

        /* Navigation Header */
        header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 20px 0;
        }

        nav {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 40px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            font-size: 1rem;
            font-weight: 400;
            color: #2d628c;
            text-decoration: none;
            text-transform: uppercase;
            letter-spacing: 0.1em;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 50px;
            margin: 0;
            padding: 0;
        }

        .nav-links a {
            color: #2d628c;
            text-decoration: none;
            font-weight: 400;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            transition: color 0.3s ease;
            opacity: 0.7;
        }

        .nav-links a:hover {
            opacity: 1;
        }

        .nav-actions {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .news-btn {
            background: #2d628c;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 400;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
        }

        .news-badge {
            background: white;
            color: #2d628c;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.65rem;
            font-weight: 600;
        }

        .news-btn:hover {
            background: #4a7ba7;
        }

        .menu-btn {
            background: transparent;
            border: none;
            color: #2d628c;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 400;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            transition: all 0.3s ease;
            padding: 0;
            opacity: 0.7;
        }

        .menu-btn:hover {
            opacity: 1;
        }

        /* Content Sections */
        .content-section {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 1;
            padding: 2rem;
            text-align: center;
            color: white;
            background-color: rgba(0, 0, 0, 0.3);
            transition: background-color 0.5s ease-in-out;
        }

        .content-section h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .content-section h2 {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .content-section p {
            font-size: 1.2rem;
            max-width: 800px;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .cta-button {
            background-color: #2d628c;
            color: white;
            padding: 0.8rem 2rem;
            border-radius: 9999px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .cta-button:hover {
            background-color: #4a7ba7;
            transform: translateY(-2px);
        }

        .content-section.night-overlay {
            background-color: rgba(0, 0, 0, 0.6);
        }
        .content-section.day-overlay {
            background-color: rgba(0, 0, 0, 0.2);
        }

        /* Services Grid */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 40px;
            margin-top: 60px;
            max-width: 800px;
        }

        .service-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .service-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-5px);
        }

        .service-item h3 {
            color: white;
            margin-bottom: 15px;
            font-size: 1.3rem;
            font-weight: 500;
        }

        .service-item p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1rem;
            margin: 0;
        }

        /* Footer */
        footer {
            min-height: 60vh;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 4rem 2rem;
            position: relative;
            z-index: 100;
            text-align: center;
            box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.5);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        footer h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #ffd700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        footer p {
            margin: 0.5rem 0;
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        footer .footer-content {
            max-width: 800px;
            margin: 0 auto;
        }

        footer .footer-links {
            display: flex;
            gap: 2rem;
            margin: 2rem 0;
            justify-content: center;
            flex-wrap: wrap;
        }

        footer .footer-links a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        footer .footer-links a:hover {
            color: #ffd700;
        }

        footer .copyright {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .nav-links {
                display: none;
            }
        }

        @media (max-width: 768px) {
            nav {
                padding: 0 20px;
            }

            .news-btn {
                display: none;
            }

            .content-section h1 {
                font-size: 2.5rem;
            }

            .content-section h2 {
                font-size: 2rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }

        /* Custom Cursor Animation */
        * {
            cursor: none;
        }

        .custom-cursor {
            position: fixed;
            top: 0;
            left: 0;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 50%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            mix-blend-mode: difference;
            transition: all 0.1s ease;
            transform: translate(-50%, -50%);
        }

        .custom-cursor.night-mode {
            background: radial-gradient(circle, rgba(255, 204, 0, 0.9) 0%, rgba(255, 204, 0, 0.5) 40%, transparent 70%);
            box-shadow: 0 0 20px rgba(255, 204, 0, 0.6);
            width: 16px;
            height: 16px;
        }

        .custom-cursor.day-mode {
            background: radial-gradient(circle, rgba(45, 98, 140, 0.8) 0%, rgba(45, 98, 140, 0.4) 50%, transparent 70%);
            box-shadow: 0 0 15px rgba(45, 98, 140, 0.4);
            width: 18px;
            height: 18px;
        }

        .custom-cursor.hover-mode {
            width: 40px;
            height: 40px;
            background: radial-gradient(circle, rgba(255, 111, 97, 0.6) 0%, rgba(255, 111, 97, 0.3) 50%, transparent 70%);
            box-shadow: 0 0 25px rgba(255, 111, 97, 0.5);
        }

        .cursor-trail {
            position: fixed;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9998;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .cursor-trail.night-trail {
            background: rgba(255, 204, 0, 0.7);
            box-shadow: 0 0 8px rgba(255, 204, 0, 0.4);
        }

        .cursor-trail.day-trail {
            background: rgba(45, 98, 140, 0.6);
            box-shadow: 0 0 6px rgba(45, 98, 140, 0.3);
        }

        /* Cursor interactions */
        a, button, .service-item {
            cursor: none;
        }

        a:hover, button:hover, .service-item:hover {
            cursor: none;
        }

        /* Particle effects for cursor */
        .cursor-particle {
            position: fixed;
            width: 2px;
            height: 2px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9997;
            animation: particle-float 2s ease-out forwards;
        }

        .cursor-particle.night-particle {
            background: rgba(255, 204, 0, 0.9);
            box-shadow: 0 0 4px rgba(255, 204, 0, 0.6);
        }

        .cursor-particle.day-particle {
            background: rgba(45, 98, 140, 0.8);
            box-shadow: 0 0 3px rgba(45, 98, 140, 0.4);
        }

        @keyframes particle-float {
            0% {
                opacity: 1;
                transform: translate(0, 0) scale(1);
            }
            100% {
                opacity: 0;
                transform: translate(var(--random-x, 0), var(--random-y, -30px)) scale(0);
            }
        }

        /* Hide cursor on mobile */
        @media (max-width: 768px) {
            .custom-cursor, .cursor-trail, .cursor-particle {
                display: none;
            }

            * {
                cursor: auto;
            }
        }
    </style>
</head>
<body>
    <!-- Custom Cursor -->
    <div class="custom-cursor" id="customCursor"></div>

    <!-- Navigation Header -->
    <header>
        <nav>
            <a href="#" class="logo">MONTFORT GROUP</a>

            <ul class="nav-links">
                <li><a href="#trading">Montfort Trading</a></li>
                <li><a href="#capital">Montfort Capital</a></li>
                <li><a href="#maritime">Montfort Maritime</a></li>
                <li><a href="#energy">Fort Energy</a></li>
            </ul>

            <div class="nav-actions">
                <a href="#news" class="news-btn">
                    NEWS
                    <span class="news-badge">01</span>
                </a>
                <button class="menu-btn" id="menuToggle">MENU</button>
            </div>
        </nav>
    </header>

    <!-- Fixed Cinematic Background Scene -->
    <div class="scene-container">
        <div class="background-gradient"></div>

        <div class="celestial-body sun" id="sun"></div>
        <div class="celestial-body moon" id="moon"></div>

        <div class="night-elements-layer" id="nightElementsLayer"></div>

        <div class="mountain-layer mountains-distant"></div>
        <div class="mountain-layer mountains-mid"></div>
        <div class="mountain-layer mountains-foreground"></div>

        <div class="clouds-layer" id="cloudsLayer">
            <div class="cloud"></div>
            <div class="cloud"></div>
            <div class="cloud"></div>
            <div class="cloud"></div>
        </div>

        <div class="trees-grass-layer" id="treesGrassLayer">
            <div class="tree-cluster type-1"></div>
            <div class="tree-cluster type-2"></div>
        </div>
    </div>

    <!-- Scrollable Content Sections -->
    <section id="section1" class="content-section">
        <h1 class="text-white">Montfort Group</h1>
        <p class="text-gray-200">A global commodity trading and asset investment company. Under the starlit sky of opportunity, we navigate the vast landscape of energy and commodities with unwavering integrity and precision.</p>
        <button>Discover Our Journey</button>
    </section>

    <section id="section2" class="content-section">
        <h1 class="text-white">Dawn of Innovation</h1>
        <p class="text-gray-200">As the sun rises on new possibilities, Montfort illuminates the path forward. We trade, refine, store, and transport energy with the brilliance of dawn, creating long-term value that brightens the future.</p>
        <button>Explore Our Vision</button>
    </section>

    <section id="section3" class="content-section">
        <h1 class="text-white">Thriving in Full Light</h1>
        <p class="text-gray-200">In the full glory of day, our four divisions shine: Montfort Trading leads with innovation, Montfort Capital maximizes value, Montfort Maritime delivers energy, and Fort Energy advances sustainable investments.</p>
        <button>View Our Divisions</button>
    </section>

    <footer>
        <div class="footer-content">
            <h2>Montfort Group</h2>
            <p>A global commodity trading and asset investment company</p>
            <p>Powering progress through integrity and innovation</p>

            <div class="footer-links">
                <a href="#trading">Montfort Trading</a>
                <a href="#capital">Montfort Capital</a>
                <a href="#maritime">Montfort Maritime</a>
                <a href="#energy">Fort Energy</a>
                <a href="#news">News</a>
                <a href="#contact">Contact</a>
            </div>

            <div class="copyright">
                <p>&copy; 2025 Montfort Group. All rights reserved.</p>
                <p>Designed with passion for excellence in commodity trading</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const sceneContainer = document.querySelector('.scene-container');
            const backgroundGradient = document.querySelector('.background-gradient');
            const sunElement = document.getElementById('sun');
            const moonElement = document.getElementById('moon');
            const mountainsDistant = document.querySelector('.mountains-distant');
            const mountainsMid = document.querySelector('.mountains-mid');
            const mountainsForeground = document.querySelector('.mountains-foreground');
            const nightElementsLayer = document.getElementById('nightElementsLayer');
            const cloudsLayer = document.getElementById('cloudsLayer');
            const treesGrassLayer = document.getElementById('treesGrassLayer');
            const contentSections = document.querySelectorAll('.content-section');

            // Calculate total scrollable height
            const totalScrollHeight = document.body.scrollHeight - window.innerHeight;

            // Define scroll thresholds for different "times of day"
            const phases = {
                nightStart: 0,
                nightEnd: 0.25,    // Section 1: Deep Night
                dawnStart: 0.20,   // Overlap for smooth transition
                dawnEnd: 0.45,     // Transition to Day
                dayStart: 0.40,    // Overlap
                dayEnd: 0.75,      // Section 2 & 3: Day
                duskStart: 0.70,   // Overlap
                duskEnd: 1.0       // Transition to Night / Footer
            };

            // Create stars
            function createStars(count) {
                for (let i = 0; i < count; i++) {
                    const star = document.createElement('div');
                    star.className = 'star';
                    star.style.left = `${Math.random() * 100}%`;
                    star.style.top = `${Math.random() * 60}%`;
                    star.style.setProperty('--star-delay', `${Math.random() * 2}s`);
                    star.style.setProperty('--star-duration', `${1.5 + Math.random() * 1}s`);
                    nightElementsLayer.appendChild(star);
                }
            }

            // Create fireflies
            function createFireflies(count) {
                for (let i = 0; i < count; i++) {
                    const firefly = document.createElement('div');
                    firefly.className = 'firefly';
                    firefly.style.left = `${20 + Math.random() * 60}%`;
                    firefly.style.top = `${50 + Math.random() * 40}%`;
                    firefly.style.animationDelay = `${Math.random() * 10}s`;
                    firefly.style.animationDuration = `${10 + Math.random() * 10}s`;
                    nightElementsLayer.appendChild(firefly);
                }
            }

            createStars(100);
            createFireflies(10);

            // Function to interpolate values smoothly
            function lerp(a, b, t) {
                return a + (b - a) * t;
            }

            // Custom Cursor System
            const customCursor = document.getElementById('customCursor');
            let mouseX = 0;
            let mouseY = 0;
            let cursorTrails = [];
            let isHovering = false;
            let currentCursorMode = 'night';

            // Create cursor trail elements
            function createCursorTrail() {
                for (let i = 0; i < 8; i++) {
                    const trail = document.createElement('div');
                    trail.className = 'cursor-trail';
                    document.body.appendChild(trail);
                    cursorTrails.push({
                        element: trail,
                        x: 0,
                        y: 0,
                        delay: i * 0.05
                    });
                }
            }

            // Update cursor position and effects
            function updateCursor(e) {
                mouseX = e.clientX;
                mouseY = e.clientY;

                customCursor.style.left = mouseX + 'px';
                customCursor.style.top = mouseY + 'px';

                // Update trail positions with delay
                cursorTrails.forEach((trail, index) => {
                    setTimeout(() => {
                        trail.x += (mouseX - trail.x) * 0.1;
                        trail.y += (mouseY - trail.y) * 0.1;
                        trail.element.style.left = trail.x + 'px';
                        trail.element.style.top = trail.y + 'px';
                        trail.element.style.opacity = (8 - index) / 8 * 0.6;
                    }, trail.delay * 1000);
                });

                // Create particles occasionally
                if (Math.random() < 0.1) {
                    createCursorParticle(mouseX, mouseY);
                }
            }

            // Create floating particles
            function createCursorParticle(x, y) {
                const particle = document.createElement('div');
                particle.className = `cursor-particle ${currentCursorMode}-particle`;

                const randomX = (Math.random() - 0.5) * 40;
                const randomY = -Math.random() * 40 - 10;

                particle.style.left = x + 'px';
                particle.style.top = y + 'px';
                particle.style.setProperty('--random-x', randomX + 'px');
                particle.style.setProperty('--random-y', randomY + 'px');

                document.body.appendChild(particle);

                setTimeout(() => {
                    particle.remove();
                }, 2000);
            }

            // Update cursor mode based on scroll position
            function updateCursorMode(scrollProgress) {
                let newMode = 'night';

                if (scrollProgress > 0.3 && scrollProgress <= 0.7) {
                    newMode = 'day';
                } else {
                    newMode = 'night';
                }

                if (newMode !== currentCursorMode) {
                    currentCursorMode = newMode;
                    customCursor.className = `custom-cursor ${isHovering ? 'hover-mode' : newMode + '-mode'}`;

                    // Update trail classes
                    cursorTrails.forEach(trail => {
                        trail.element.className = `cursor-trail ${newMode}-trail`;
                    });
                }
            }

            // Handle hover states
            function handleMouseEnter() {
                isHovering = true;
                customCursor.classList.add('hover-mode');
            }

            function handleMouseLeave() {
                isHovering = false;
                customCursor.classList.remove('hover-mode');
                customCursor.className = `custom-cursor ${currentCursorMode}-mode`;
            }

            // Initialize cursor system
            createCursorTrail();

            // Add event listeners for cursor
            document.addEventListener('mousemove', updateCursor);

            // Add hover effects to interactive elements
            const interactiveElements = document.querySelectorAll('a, button, .service-item');
            interactiveElements.forEach(element => {
                element.addEventListener('mouseenter', handleMouseEnter);
                element.addEventListener('mouseleave', handleMouseLeave);
            });

            // Function to update scene elements based on scroll
            function updateSceneOnScroll() {
                const scrollY = window.scrollY;
                const scrollProgress = scrollY / totalScrollHeight; // 0 to 1

                // --- 1. Background Gradient Pan ---
                const gradientTotalMovement = backgroundGradient.offsetHeight - window.innerHeight;
                const gradientPanAmount = scrollProgress * gradientTotalMovement;
                backgroundGradient.style.transform = `translateY(-${gradientPanAmount}px)`;

                // --- 2. Mountain Parallax ---
                mountainsDistant.style.transform = `translateX(${scrollProgress * 200}px)`;
                mountainsMid.style.transform = `translateX(-${scrollProgress * 300}px)`;
                mountainsForeground.style.transform = `translateX(${scrollProgress * 500}px)`;

                // --- 3. Dynamic Opacity/Color for elements based on "time of day" ---
                let nightOpacity = 0;
                let cloudsOpacity = 0;
                let treesOpacity = 0;
                let treeBrightness = 1;
                let sunOpacity = 0;
                let moonOpacity = 0;
                let sunTop = 120; // %
                let moonTop = -20; // %
                let sectionOverlayAlpha = 0.3; // Default alpha for content overlay

                // Night (Section 1)
                if (scrollProgress >= phases.nightStart && scrollProgress <= phases.nightEnd) {
                    const t = (scrollProgress - phases.nightStart) / (phases.nightEnd - phases.nightStart);
                    nightOpacity = lerp(1, 0, t);
                    moonOpacity = lerp(1, 0, t);
                    moonTop = lerp(-20, 100, t);
                    cloudsOpacity = lerp(0.2, 0.8, t);
                    treesOpacity = lerp(0.5, 1, t);
                    treeBrightness = lerp(0.3, 1, t);
                    sectionOverlayAlpha = lerp(0.6, 0.3, t);
                }
                // Dawn/Sunrise (Transition from Section 1 to 2)
                else if (scrollProgress > phases.dawnStart && scrollProgress <= phases.dawnEnd) {
                    const t = (scrollProgress - phases.dawnStart) / (phases.dawnEnd - phases.dawnStart);
                    nightOpacity = lerp(0, 0, t);
                    moonOpacity = lerp(0, 0, t);
                    sunOpacity = lerp(0, 1, t);
                    sunTop = lerp(120, 10, t);
                    cloudsOpacity = lerp(0.8, 1, t);
                    treesOpacity = lerp(1, 1, t);
                    treeBrightness = lerp(1, 1, t);
                    sectionOverlayAlpha = lerp(0.3, 0.2, t);
                }
                // Mid-Day (Section 2 & 3)
                else if (scrollProgress > phases.dayStart && scrollProgress <= phases.dayEnd) {
                    nightOpacity = 0;
                    moonOpacity = 0;
                    sunOpacity = 1;
                    sunTop = 10;
                    cloudsOpacity = 1;
                    treesOpacity = 1;
                    treeBrightness = 1;
                    sectionOverlayAlpha = 0.2;
                }
                // Dusk/Early Night (Transition from Section 3 to Footer)
                else if (scrollProgress > phases.duskStart && scrollProgress <= phases.duskEnd) {
                    const t = (scrollProgress - phases.duskStart) / (phases.duskEnd - phases.duskStart);
                    nightOpacity = lerp(0, 1, t);
                    moonOpacity = lerp(0, 1, t);
                    moonTop = lerp(100, -20, t);
                    sunOpacity = lerp(1, 0, t);
                    sunTop = lerp(10, 120, t);
                    cloudsOpacity = lerp(1, 0.2, t);
                    treesOpacity = lerp(1, 0.5, t);
                    treeBrightness = lerp(1, 0.3, t);
                    sectionOverlayAlpha = lerp(0.2, 0.6, t);
                }

                // Apply celestial body positions and opacity
                nightElementsLayer.style.opacity = nightOpacity;
                cloudsLayer.style.opacity = cloudsOpacity;
                treesGrassLayer.style.opacity = treesOpacity;
                sunElement.style.opacity = sunOpacity;
                sunElement.style.top = `${sunTop}%`;
                moonElement.style.opacity = moonOpacity;
                moonElement.style.top = `${moonTop}%`;

                // Adjust tree colors based on brightness
                document.querySelectorAll('.tree-cluster').forEach(tree => {
                    const r = Math.round(34 * treeBrightness);
                    const g = Math.round(139 * treeBrightness);
                    const b = Math.round(34 * treeBrightness);
                    tree.style.setProperty('--tree-color', `rgb(${r}, ${g}, ${b})`);
                    tree.style.filter = `brightness(${treeBrightness})`;
                });

                // Adjust mountain colors based on time of day
                const mountainColorNight = 'rgba(10, 10, 42, 1)';
                const mountainColorDawn = 'rgba(40, 10, 60, 1)';
                const mountainColorDay = 'rgba(60, 60, 80, 1)';
                const mountainColorDusk = 'rgba(30, 10, 50, 1)';

                let currentMountainColor;
                if (scrollProgress <= phases.nightEnd) {
                    currentMountainColor = mountainColorNight;
                } else if (scrollProgress > phases.dawnStart && scrollProgress <= phases.dawnEnd) {
                    const t = (scrollProgress - phases.dawnStart) / (phases.dawnEnd - phases.dawnStart);
                    currentMountainColor = `rgba(${lerp(10, 40, t)}, ${lerp(10, 10, t)}, ${lerp(42, 60, t)}, 1)`;
                } else if (scrollProgress > phases.dayStart && scrollProgress <= phases.dayEnd) {
                    currentMountainColor = mountainColorDay;
                } else if (scrollProgress > phases.duskStart) {
                    const t = (scrollProgress - phases.duskStart) / (phases.duskEnd - phases.duskStart);
                    currentMountainColor = `rgba(${lerp(60, 30, t)}, ${lerp(60, 10, t)}, ${lerp(80, 50, t)}, 1)`;
                }

                mountainsDistant.style.backgroundColor = currentMountainColor.replace(', 1)', ', 0.8)');
                mountainsMid.style.backgroundColor = currentMountainColor.replace(', 1)', ', 0.9)');
                mountainsForeground.style.backgroundColor = currentMountainColor;

                // Adjust content section overlays
                contentSections.forEach(section => {
                    section.style.backgroundColor = `rgba(0, 0, 0, ${sectionOverlayAlpha})`;
                });

                // Update cursor mode based on scroll progress
                updateCursorMode(scrollProgress);
            }

            // Attach scroll event listener
            window.addEventListener('scroll', updateSceneOnScroll);

            // Initial call to set the scene based on initial scroll position
            updateSceneOnScroll();

            // Menu functionality
            const menuToggle = document.getElementById('menuToggle');
            if (menuToggle) {
                menuToggle.addEventListener('click', function() {
                    // Add menu functionality here if needed
                    console.log('Menu clicked');
                });
            }

            console.log('Cinematic Mont-Fort experience initialized');
        });
    </script>
</body>
</html>
