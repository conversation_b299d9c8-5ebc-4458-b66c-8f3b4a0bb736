// GlobalApp stub
class GlobalApp {
    constructor() {
        this.state = {
            emit: (event, data) => {
                console.log('Event emitted:', event, data);
            }
        };
        this.tools = {
            viewport: {
                infos: { width: window.innerWidth, height: window.innerHeight }
            }
        };
    }

    static init(wrapper, canvas) {
        // Initialize global app functionality
        document.body.classList.add('loaded');
        console.log('GlobalApp initialized with WebGL');
        return Promise.resolve(new GlobalApp());
    }
}

// WebGL app instance with proper implementation
const webglApp = {
    canvas: null,
    gl: null,
    animationId: null,
    time: 0,

    state: {
        emit: (event, data) => {
            console.log('WebGL Event:', event, data);
        }
    },

    tools: {
        viewport: {
            infos: { width: window.innerWidth, height: window.innerHeight }
        }
    },

    // Vertex shader source
    vertexShaderSource: `
        attribute vec2 a_position;
        varying vec2 v_uv;
        void main() {
            v_uv = a_position * 0.5 + 0.5;
            gl_Position = vec4(a_position, 0.0, 1.0);
        }
    `,

    // Fragment shader source with animated gradient
    fragmentShaderSource: `
        precision mediump float;
        varying vec2 v_uv;
        uniform float u_time;
        uniform vec2 u_resolution;

        void main() {
            vec2 uv = v_uv;

            // Create animated gradient
            float wave1 = sin(uv.x * 3.14159 + u_time * 0.5) * 0.5 + 0.5;
            float wave2 = cos(uv.y * 3.14159 + u_time * 0.3) * 0.5 + 0.5;

            // Montfort blue colors
            vec3 color1 = vec3(0.176, 0.384, 0.549); // #2d628c
            vec3 color2 = vec3(0.290, 0.482, 0.655); // #4a7ba7
            vec3 color3 = vec3(0.549, 0.706, 0.835); // #8cb4d5

            // Mix colors based on waves
            vec3 finalColor = mix(color1, color2, wave1);
            finalColor = mix(finalColor, color3, wave2 * 0.3);

            // Add subtle noise
            float noise = fract(sin(dot(uv, vec2(12.9898, 78.233))) * 43758.5453);
            finalColor += noise * 0.02;

            gl_FragColor = vec4(finalColor, 1.0);
        }
    `,

    createShader: function(type, source) {
        const shader = this.gl.createShader(type);
        this.gl.shaderSource(shader, source);
        this.gl.compileShader(shader);

        if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
            console.error('Shader compilation error:', this.gl.getShaderInfoLog(shader));
            this.gl.deleteShader(shader);
            return null;
        }

        return shader;
    },

    createProgram: function(vertexShader, fragmentShader) {
        const program = this.gl.createProgram();
        this.gl.attachShader(program, vertexShader);
        this.gl.attachShader(program, fragmentShader);
        this.gl.linkProgram(program);

        if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
            console.error('Program linking error:', this.gl.getProgramInfoLog(program));
            this.gl.deleteProgram(program);
            return null;
        }

        return program;
    },

    render: function() {
        this.time += 0.016; // ~60fps

        // Resize canvas if needed
        if (this.canvas.width !== window.innerWidth || this.canvas.height !== window.innerHeight) {
            this.canvas.width = window.innerWidth;
            this.canvas.height = window.innerHeight;
            this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
        }

        // Clear and draw
        this.gl.clear(this.gl.COLOR_BUFFER_BIT);

        // Update uniforms
        this.gl.uniform1f(this.timeLocation, this.time);
        this.gl.uniform2f(this.resolutionLocation, this.canvas.width, this.canvas.height);

        // Draw
        this.gl.drawArrays(this.gl.TRIANGLES, 0, 6);

        // Continue animation
        this.animationId = requestAnimationFrame(() => this.render());
    },

    init: async (wrapper, canvas) => {
        console.log('WebGL initialized with wrapper:', wrapper, 'canvas:', canvas);

        if (!canvas) {
            console.error('No canvas provided');
            return webglApp;
        }

        webglApp.canvas = canvas;
        webglApp.gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

        if (!webglApp.gl) {
            console.log('WebGL not supported, using CSS fallback');
            if (wrapper) {
                wrapper.style.background = 'linear-gradient(135deg, #2d628c 0%, #4a7ba7 100%)';
            }
            return webglApp;
        }

        console.log('WebGL context created successfully');

        // Set canvas size
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        webglApp.gl.viewport(0, 0, canvas.width, canvas.height);

        // Create shaders
        const vertexShader = webglApp.createShader(webglApp.gl.VERTEX_SHADER, webglApp.vertexShaderSource);
        const fragmentShader = webglApp.createShader(webglApp.gl.FRAGMENT_SHADER, webglApp.fragmentShaderSource);

        if (!vertexShader || !fragmentShader) {
            console.error('Failed to create shaders');
            return webglApp;
        }

        // Create program
        const program = webglApp.createProgram(vertexShader, fragmentShader);
        if (!program) {
            console.error('Failed to create program');
            return webglApp;
        }

        webglApp.gl.useProgram(program);

        // Create fullscreen quad
        const positions = new Float32Array([
            -1, -1,
             1, -1,
            -1,  1,
            -1,  1,
             1, -1,
             1,  1
        ]);

        const positionBuffer = webglApp.gl.createBuffer();
        webglApp.gl.bindBuffer(webglApp.gl.ARRAY_BUFFER, positionBuffer);
        webglApp.gl.bufferData(webglApp.gl.ARRAY_BUFFER, positions, webglApp.gl.STATIC_DRAW);

        // Get attribute and uniform locations
        const positionLocation = webglApp.gl.getAttribLocation(program, 'a_position');
        webglApp.timeLocation = webglApp.gl.getUniformLocation(program, 'u_time');
        webglApp.resolutionLocation = webglApp.gl.getUniformLocation(program, 'u_resolution');

        // Enable attribute
        webglApp.gl.enableVertexAttribArray(positionLocation);
        webglApp.gl.vertexAttribPointer(positionLocation, 2, webglApp.gl.FLOAT, false, 0, 0);

        // Start rendering
        webglApp.render();

        // Handle resize
        window.addEventListener('resize', () => {
            webglApp.tools.viewport.infos = {
                width: window.innerWidth,
                height: window.innerHeight
            };
        });

        return webglApp;
    }
};

// Events enum
const Events = {
    ATTACH: 'ATTACH',
    RESIZE: 'RESIZE'
};

// Export as named exports to match the imports
export { GlobalApp as G };
export { webglApp as a };
export { Events as E };
export default GlobalApp;
