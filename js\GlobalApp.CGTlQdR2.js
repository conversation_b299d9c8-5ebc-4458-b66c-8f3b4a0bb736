// GlobalApp stub
class GlobalApp {
    constructor() {
        this.state = {
            emit: (event, data) => {
                console.log('Event emitted:', event, data);
            }
        };
        this.tools = {
            viewport: {
                infos: { width: window.innerWidth, height: window.innerHeight }
            }
        };
    }

    static init(wrapper, canvas) {
        // Initialize global app functionality
        document.body.classList.add('loaded');
        console.log('GlobalApp initialized with WebGL');
        return Promise.resolve(new GlobalApp());
    }
}

// WebGL app instance with proper implementation
const webglApp = {
    canvas: null,
    gl: null,
    animationId: null,
    time: 0,

    // Interactive state
    mouse: { x: 0.5, y: 0.5 },
    scrollProgress: 0,
    currentScene: 0,
    targetScene: 0,
    sceneTransition: 0,

    // Textures and scenes
    textures: [],
    currentTexture: null,
    nextTexture: null,

    // Scene configuration - different mountain images for different chapters
    // Using the same image structure as the original mont-fort.com site
    scenes: [
        { name: 'Hero', image: 'images/_astro-m_Lob0Q.png', colors: [0.176, 0.384, 0.549] },
        { name: 'WhoWeAre', image: 'images/_astro-m_Z1SYCbs.png', colors: [0.290, 0.482, 0.655] },
        { name: 'WhatWeDo', image: 'images/_astro-m_Z2sBiVc.png', colors: [0.549, 0.706, 0.835] },
        { name: 'GlobalConnectivity', image: 'images/_astro-m_ZBce86.png', colors: [0.176, 0.384, 0.549] },
        { name: 'Sustainability', image: 'images/_astro-m_ZeiBvl.png', colors: [0.290, 0.482, 0.655] }
    ],

    state: {
        emit: (event, data) => {
            console.log('WebGL Event:', event, data);
        }
    },

    tools: {
        viewport: {
            infos: { width: window.innerWidth, height: window.innerHeight }
        }
    },

    // Vertex shader source
    vertexShaderSource: `
        attribute vec2 a_position;
        varying vec2 v_uv;
        void main() {
            v_uv = a_position * 0.5 + 0.5;
            gl_Position = vec4(a_position, 0.0, 1.0);
        }
    `,

    // Interactive fragment shader with scene transitions and mouse effects
    fragmentShaderSource: `
        precision mediump float;
        varying vec2 v_uv;
        uniform float u_time;
        uniform vec2 u_resolution;
        uniform vec2 u_mouse;
        uniform float u_scrollProgress;
        uniform float u_sceneTransition;
        uniform sampler2D u_currentTexture;
        uniform sampler2D u_nextTexture;
        uniform vec3 u_sceneColor;
        uniform vec3 u_nextSceneColor;

        void main() {
            vec2 uv = v_uv;

            // Mouse interaction - create ripple effect around cursor
            vec2 mousePos = u_mouse;
            float mouseDistance = distance(uv, mousePos);
            float mouseEffect = 1.0 - smoothstep(0.0, 0.3, mouseDistance);
            mouseEffect *= sin(u_time * 3.0 - mouseDistance * 10.0) * 0.1 + 0.9;

            // Parallax layers based on mouse position
            vec2 parallax1 = uv + (mousePos - 0.5) * 0.02 + vec2(u_time * 0.001, 0.0);
            vec2 parallax2 = uv + (mousePos - 0.5) * 0.01 + vec2(u_time * 0.0005, 0.0);

            // Sample mountain textures for scene transition
            vec4 currentMountain = texture2D(u_currentTexture, parallax1);
            vec4 nextMountain = texture2D(u_nextTexture, parallax2);

            // Blend between scenes based on transition progress
            vec4 mountainColor = mix(currentMountain, nextMountain, u_sceneTransition);

            // Create animated gradient that responds to mouse and scroll
            float wave1 = sin(uv.x * 3.14159 + u_time * 0.5 + mouseEffect * 2.0) * 0.5 + 0.5;
            float wave2 = cos(uv.y * 3.14159 + u_time * 0.3 + u_scrollProgress * 2.0) * 0.5 + 0.5;

            // Scene colors that transition
            vec3 currentColor = u_sceneColor;
            vec3 nextColor = u_nextSceneColor;
            vec3 sceneColor = mix(currentColor, nextColor, u_sceneTransition);

            // Create gradient
            vec3 gradientColor = mix(sceneColor * 0.8, sceneColor * 1.2, wave1);
            gradientColor = mix(gradientColor, sceneColor * 1.4, wave2 * 0.3);

            // Blend mountain and gradient based on mouse interaction and scroll
            float blendFactor = 0.6 + mouseEffect * 0.3 + sin(u_scrollProgress * 6.28) * 0.1;
            vec3 finalColor = mix(gradientColor, mountainColor.rgb * 1.2, blendFactor);

            // Add atmospheric depth
            float depth = smoothstep(0.0, 1.0, uv.y);
            vec3 atmosphereColor = mix(sceneColor, vec3(0.7, 0.8, 0.9), 0.5);
            finalColor = mix(finalColor, atmosphereColor, depth * 0.2);

            // Mouse cursor glow effect
            float cursorGlow = 1.0 - smoothstep(0.0, 0.15, mouseDistance);
            finalColor += cursorGlow * sceneColor * 0.3;

            // Add subtle noise for texture
            float noise = fract(sin(dot(uv, vec2(12.9898, 78.233))) * 43758.5453);
            finalColor += noise * 0.02;

            gl_FragColor = vec4(finalColor, 1.0);
        }
    `,

    createShader: function(type, source) {
        const shader = this.gl.createShader(type);
        this.gl.shaderSource(shader, source);
        this.gl.compileShader(shader);

        if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
            console.error('Shader compilation error:', this.gl.getShaderInfoLog(shader));
            this.gl.deleteShader(shader);
            return null;
        }

        return shader;
    },

    createProgram: function(vertexShader, fragmentShader) {
        const program = this.gl.createProgram();
        this.gl.attachShader(program, vertexShader);
        this.gl.attachShader(program, fragmentShader);
        this.gl.linkProgram(program);

        if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
            console.error('Program linking error:', this.gl.getProgramInfoLog(program));
            this.gl.deleteProgram(program);
            return null;
        }

        return program;
    },

    setupInteraction: function() {
        // Mouse tracking
        document.addEventListener('mousemove', (e) => {
            this.mouse.x = e.clientX / window.innerWidth;
            this.mouse.y = 1.0 - (e.clientY / window.innerHeight); // Flip Y for WebGL
        });

        // Scroll tracking with scene detection
        window.addEventListener('scroll', () => {
            const scrollY = window.scrollY;
            const maxScroll = document.body.scrollHeight - window.innerHeight;
            this.scrollProgress = Math.min(scrollY / Math.max(maxScroll, 1), 1.0);

            // Detect current chapter/scene based on scroll position
            const chapters = document.querySelectorAll('[data-chapter]');
            let newScene = 0;

            chapters.forEach((chapter, index) => {
                const rect = chapter.getBoundingClientRect();
                const isVisible = rect.top < window.innerHeight * 0.5 && rect.bottom > window.innerHeight * 0.5;
                if (isVisible && index < this.scenes.length) {
                    newScene = index;
                }
            });

            // Trigger scene transition if scene changed
            if (newScene !== this.targetScene) {
                this.targetScene = newScene;
                console.log(`Scene transition to: ${this.scenes[newScene]?.name || 'Unknown'}`);
            }
        });

        // Cursor interaction effects
        document.addEventListener('mousedown', () => {
            document.body.style.cursor = 'grabbing';
        });

        document.addEventListener('mouseup', () => {
            document.body.style.cursor = 'grab';
        });

        console.log('Interactive controls initialized');
    },

    loadTexture: function(url) {
        return new Promise((resolve, reject) => {
            const texture = this.gl.createTexture();
            this.gl.bindTexture(this.gl.TEXTURE_2D, texture);

            // Fill with a temporary color while loading
            this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, 1, 1, 0, this.gl.RGBA, this.gl.UNSIGNED_BYTE,
                new Uint8Array([100, 150, 200, 255])); // Mountain blue placeholder

            const image = new Image();
            image.crossOrigin = 'anonymous';

            image.onload = () => {
                console.log('Mountain texture loaded:', url);
                this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
                this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, this.gl.RGBA, this.gl.UNSIGNED_BYTE, image);

                // Set texture parameters for mountain images
                this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
                this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);
                this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR);
                this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.LINEAR);

                resolve(texture);
            };

            image.onerror = () => {
                console.warn('Failed to load mountain texture:', url);
                // Use placeholder texture
                resolve(texture);
            };

            image.src = url;
        });
    },

    render: function() {
        this.time += 0.016; // ~60fps

        // Resize canvas if needed
        if (this.canvas.width !== window.innerWidth || this.canvas.height !== window.innerHeight) {
            this.canvas.width = window.innerWidth;
            this.canvas.height = window.innerHeight;
            this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
        }

        // Update scene transition
        if (this.currentScene !== this.targetScene) {
            this.sceneTransition += 0.02; // Smooth transition speed
            if (this.sceneTransition >= 1.0) {
                this.currentScene = this.targetScene;
                this.sceneTransition = 0.0;

                // Update textures for next transition
                if (this.textures[this.currentScene]) {
                    this.currentTexture = this.textures[this.currentScene];
                }
            }
        } else {
            this.sceneTransition = Math.max(0.0, this.sceneTransition - 0.01);
        }

        // Clear and draw
        this.gl.clear(this.gl.COLOR_BUFFER_BIT);

        // Update basic uniforms
        this.gl.uniform1f(this.timeLocation, this.time);
        this.gl.uniform2f(this.resolutionLocation, this.canvas.width, this.canvas.height);

        // Update interactive uniforms
        this.gl.uniform2f(this.mouseLocation, this.mouse.x, this.mouse.y);
        this.gl.uniform1f(this.scrollProgressLocation, this.scrollProgress);
        this.gl.uniform1f(this.sceneTransitionLocation, this.sceneTransition);

        // Update scene colors
        const currentScene = this.scenes[this.currentScene] || this.scenes[0];
        const nextScene = this.scenes[this.targetScene] || this.scenes[0];
        this.gl.uniform3f(this.sceneColorLocation, ...currentScene.colors);
        this.gl.uniform3f(this.nextSceneColorLocation, ...nextScene.colors);

        // Bind textures
        if (this.currentTexture) {
            this.gl.activeTexture(this.gl.TEXTURE0);
            this.gl.bindTexture(this.gl.TEXTURE_2D, this.currentTexture);
            this.gl.uniform1i(this.currentTextureLocation, 0);
        }

        if (this.nextTexture && this.sceneTransition > 0) {
            this.gl.activeTexture(this.gl.TEXTURE1);
            this.gl.bindTexture(this.gl.TEXTURE_2D, this.nextTexture);
            this.gl.uniform1i(this.nextTextureLocation, 1);
        }

        // Draw
        this.gl.drawArrays(this.gl.TRIANGLES, 0, 6);

        // Continue animation
        this.animationId = requestAnimationFrame(() => this.render());
    },

    init: async (wrapper, canvas) => {
        console.log('WebGL initialized with wrapper:', wrapper, 'canvas:', canvas);

        if (!canvas) {
            console.error('No canvas provided');
            return webglApp;
        }

        webglApp.canvas = canvas;
        webglApp.gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

        if (!webglApp.gl) {
            console.log('WebGL not supported, using CSS fallback');
            if (wrapper) {
                wrapper.style.background = 'linear-gradient(135deg, #2d628c 0%, #4a7ba7 100%)';
            }
            return webglApp;
        }

        console.log('WebGL context created successfully');

        // Set canvas size
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        webglApp.gl.viewport(0, 0, canvas.width, canvas.height);

        // Create shaders
        const vertexShader = webglApp.createShader(webglApp.gl.VERTEX_SHADER, webglApp.vertexShaderSource);
        const fragmentShader = webglApp.createShader(webglApp.gl.FRAGMENT_SHADER, webglApp.fragmentShaderSource);

        if (!vertexShader || !fragmentShader) {
            console.error('Failed to create shaders');
            return webglApp;
        }

        // Create program
        const program = webglApp.createProgram(vertexShader, fragmentShader);
        if (!program) {
            console.error('Failed to create program');
            return webglApp;
        }

        webglApp.gl.useProgram(program);

        // Create fullscreen quad
        const positions = new Float32Array([
            -1, -1,
             1, -1,
            -1,  1,
            -1,  1,
             1, -1,
             1,  1
        ]);

        const positionBuffer = webglApp.gl.createBuffer();
        webglApp.gl.bindBuffer(webglApp.gl.ARRAY_BUFFER, positionBuffer);
        webglApp.gl.bufferData(webglApp.gl.ARRAY_BUFFER, positions, webglApp.gl.STATIC_DRAW);

        // Get attribute and uniform locations
        const positionLocation = webglApp.gl.getAttribLocation(program, 'a_position');
        webglApp.timeLocation = webglApp.gl.getUniformLocation(program, 'u_time');
        webglApp.resolutionLocation = webglApp.gl.getUniformLocation(program, 'u_resolution');
        webglApp.mouseLocation = webglApp.gl.getUniformLocation(program, 'u_mouse');
        webglApp.scrollProgressLocation = webglApp.gl.getUniformLocation(program, 'u_scrollProgress');
        webglApp.sceneTransitionLocation = webglApp.gl.getUniformLocation(program, 'u_sceneTransition');
        webglApp.currentTextureLocation = webglApp.gl.getUniformLocation(program, 'u_currentTexture');
        webglApp.nextTextureLocation = webglApp.gl.getUniformLocation(program, 'u_nextTexture');
        webglApp.sceneColorLocation = webglApp.gl.getUniformLocation(program, 'u_sceneColor');
        webglApp.nextSceneColorLocation = webglApp.gl.getUniformLocation(program, 'u_nextSceneColor');

        // Enable attribute
        webglApp.gl.enableVertexAttribArray(positionLocation);
        webglApp.gl.vertexAttribPointer(positionLocation, 2, webglApp.gl.FLOAT, false, 0, 0);

        // Load all scene textures
        console.log('Loading scene textures...');

        const loadSceneTextures = async () => {
            const loadPromises = webglApp.scenes.map(async (scene, index) => {
                try {
                    console.log(`Loading scene ${index + 1} texture:`, scene.image);
                    const texture = await webglApp.loadTexture(scene.image);
                    webglApp.textures[index] = texture;
                    console.log(`Scene ${index + 1} (${scene.name}) texture loaded successfully`);
                    return texture;
                } catch (error) {
                    console.warn(`Failed to load scene ${index + 1} texture:`, scene.image, error);
                    return null;
                }
            });

            await Promise.all(loadPromises);

            // Set initial textures
            if (webglApp.textures[0]) {
                webglApp.currentTexture = webglApp.textures[0];
                console.log('Initial scene texture set');
            }

            if (webglApp.textures[1]) {
                webglApp.nextTexture = webglApp.textures[1];
            }

            console.log(`Loaded ${webglApp.textures.filter(t => t).length} scene textures`);
        };

        // Start loading textures asynchronously
        loadSceneTextures();

        // Setup interactive controls
        webglApp.setupInteraction();

        // Start rendering
        webglApp.render();

        // Handle resize
        window.addEventListener('resize', () => {
            webglApp.tools.viewport.infos = {
                width: window.innerWidth,
                height: window.innerHeight
            };
        });

        console.log('Interactive WebGL mountain background initialized');
        return webglApp;
    }
};

// Events enum
const Events = {
    ATTACH: 'ATTACH',
    RESIZE: 'RESIZE'
};

// Export as named exports to match the imports
export { GlobalApp as G };
export { webglApp as a };
export { Events as E };
export default GlobalApp;
