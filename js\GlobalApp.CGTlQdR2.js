// GlobalApp stub
class GlobalApp {
    constructor() {
        this.state = {
            emit: (event, data) => {
                console.log('Event emitted:', event, data);
            }
        };
        this.tools = {
            viewport: {
                infos: { width: window.innerWidth, height: window.innerHeight }
            }
        };
    }

    static init(wrapper, canvas) {
        // Initialize global app functionality
        document.body.classList.add('loaded');
        console.log('GlobalApp initialized with WebGL');
        return Promise.resolve(new GlobalApp());
    }
}

// WebGL app instance
const webglApp = {
    state: {
        emit: (event, data) => {
            console.log('WebGL Event:', event, data);
        }
    },
    tools: {
        viewport: {
            infos: { width: window.innerWidth, height: window.innerHeight }
        }
    },
    init: async (wrapper, canvas) => {
        console.log('WebGL initialized with wrapper:', wrapper, 'canvas:', canvas);

        // Initialize basic WebGL context
        if (canvas) {
            const ctx = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (ctx) {
                // Set canvas size
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;

                // Set basic WebGL background
                ctx.clearColor(0.17, 0.38, 0.55, 1.0); // Montfort blue color
                ctx.clear(ctx.COLOR_BUFFER_BIT);

                console.log('WebGL context created successfully');
            } else {
                console.log('WebGL not supported, using fallback');
                // Set a CSS background as fallback
                if (wrapper) {
                    wrapper.style.background = 'linear-gradient(135deg, #2d628c 0%, #4a7ba7 100%)';
                }
            }
        }

        return webglApp;
    }
};

// Events enum
const Events = {
    ATTACH: 'ATTACH',
    RESIZE: 'RESIZE'
};

// Export as named exports to match the imports
export { GlobalApp as G };
export { webglApp as a };
export { Events as E };
export default GlobalApp;
