// GlobalApp stub
class GlobalApp {
    constructor() {
        this.state = {
            emit: (event, data) => {
                console.log('Event emitted:', event, data);
            }
        };
        this.tools = {
            viewport: {
                infos: { width: window.innerWidth, height: window.innerHeight }
            }
        };
    }

    static init(wrapper, canvas) {
        // Initialize global app functionality
        document.body.classList.add('loaded');
        console.log('GlobalApp initialized with WebGL');
        return Promise.resolve(new GlobalApp());
    }
}

// WebGL app instance with proper implementation
const webglApp = {
    canvas: null,
    gl: null,
    animationId: null,
    time: 0,
    texture: null,
    mountainImages: [
        'images/_astro-m_Z1IABvS.png',
        'images/_astro-m_Z1SYCbs.png',
        'images/_astro-m_Z1r3Ar6.png',
        'images/_astro-m_Z2sBiVc.png',
        'images/_astro-m_ZBce86.png',
        'images/_astro-m_ZeiBvl.png',
        'images/_astro-m_Zrb5M9.png'
    ],

    state: {
        emit: (event, data) => {
            console.log('WebGL Event:', event, data);
        }
    },

    tools: {
        viewport: {
            infos: { width: window.innerWidth, height: window.innerHeight }
        }
    },

    // Vertex shader source
    vertexShaderSource: `
        attribute vec2 a_position;
        varying vec2 v_uv;
        void main() {
            v_uv = a_position * 0.5 + 0.5;
            gl_Position = vec4(a_position, 0.0, 1.0);
        }
    `,

    // Fragment shader source with enhanced mountain texture
    fragmentShaderSource: `
        precision mediump float;
        varying vec2 v_uv;
        uniform float u_time;
        uniform vec2 u_resolution;
        uniform sampler2D u_texture;
        uniform float u_parallax;

        void main() {
            vec2 uv = v_uv;

            // Create subtle parallax effect for depth
            vec2 parallaxUV = uv + vec2(u_time * 0.002, 0.0);

            // Sample mountain texture
            vec4 mountainColor = texture2D(u_texture, parallaxUV);

            // Enhance mountain visibility and contrast
            mountainColor.rgb = pow(mountainColor.rgb, vec3(0.8)); // Increase contrast
            mountainColor.rgb *= 1.3; // Increase brightness

            // Add atmospheric depth - mountains fade to blue in distance
            float depth = smoothstep(0.0, 1.0, uv.y);
            vec3 atmosphereColor = vec3(0.6, 0.75, 0.9); // Light blue atmosphere
            mountainColor.rgb = mix(mountainColor.rgb, atmosphereColor, depth * 0.4);

            // Add subtle mist/cloud movement
            float mist = sin(uv.x * 4.0 + u_time * 0.1) * sin(uv.y * 2.0 + u_time * 0.15);
            mist = smoothstep(-0.3, 0.3, mist) * 0.1;
            mountainColor.rgb += mist * vec3(0.9, 0.95, 1.0);

            // Ensure mountains are clearly visible
            mountainColor.rgb = clamp(mountainColor.rgb, 0.0, 1.0);

            gl_FragColor = vec4(mountainColor.rgb, 1.0);
        }
    `,

    createShader: function(type, source) {
        const shader = this.gl.createShader(type);
        this.gl.shaderSource(shader, source);
        this.gl.compileShader(shader);

        if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
            console.error('Shader compilation error:', this.gl.getShaderInfoLog(shader));
            this.gl.deleteShader(shader);
            return null;
        }

        return shader;
    },

    createProgram: function(vertexShader, fragmentShader) {
        const program = this.gl.createProgram();
        this.gl.attachShader(program, vertexShader);
        this.gl.attachShader(program, fragmentShader);
        this.gl.linkProgram(program);

        if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
            console.error('Program linking error:', this.gl.getProgramInfoLog(program));
            this.gl.deleteProgram(program);
            return null;
        }

        return program;
    },

    loadTexture: function(url) {
        return new Promise((resolve, reject) => {
            const texture = this.gl.createTexture();
            this.gl.bindTexture(this.gl.TEXTURE_2D, texture);

            // Fill with a temporary color while loading
            this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, 1, 1, 0, this.gl.RGBA, this.gl.UNSIGNED_BYTE,
                new Uint8Array([100, 150, 200, 255])); // Mountain blue placeholder

            const image = new Image();
            image.crossOrigin = 'anonymous';

            image.onload = () => {
                console.log('Mountain texture loaded:', url);
                this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
                this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, this.gl.RGBA, this.gl.UNSIGNED_BYTE, image);

                // Set texture parameters for mountain images
                this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
                this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);
                this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR);
                this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.LINEAR);

                resolve(texture);
            };

            image.onerror = () => {
                console.warn('Failed to load mountain texture:', url);
                // Use placeholder texture
                resolve(texture);
            };

            image.src = url;
        });
    },

    render: function() {
        this.time += 0.016; // ~60fps

        // Resize canvas if needed
        if (this.canvas.width !== window.innerWidth || this.canvas.height !== window.innerHeight) {
            this.canvas.width = window.innerWidth;
            this.canvas.height = window.innerHeight;
            this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
        }

        // Clear and draw
        this.gl.clear(this.gl.COLOR_BUFFER_BIT);

        // Update uniforms
        this.gl.uniform1f(this.timeLocation, this.time);
        this.gl.uniform2f(this.resolutionLocation, this.canvas.width, this.canvas.height);

        // Bind mountain texture
        if (this.texture) {
            this.gl.activeTexture(this.gl.TEXTURE0);
            this.gl.bindTexture(this.gl.TEXTURE_2D, this.texture);
            this.gl.uniform1i(this.textureLocation, 0);
        }

        // Update parallax uniform for subtle movement
        this.gl.uniform1f(this.parallaxLocation, this.time * 0.1);

        // Draw
        this.gl.drawArrays(this.gl.TRIANGLES, 0, 6);

        // Continue animation
        this.animationId = requestAnimationFrame(() => this.render());
    },

    init: async (wrapper, canvas) => {
        console.log('WebGL initialized with wrapper:', wrapper, 'canvas:', canvas);

        if (!canvas) {
            console.error('No canvas provided');
            return webglApp;
        }

        webglApp.canvas = canvas;
        webglApp.gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

        if (!webglApp.gl) {
            console.log('WebGL not supported, using CSS fallback');
            if (wrapper) {
                wrapper.style.background = 'linear-gradient(135deg, #2d628c 0%, #4a7ba7 100%)';
            }
            return webglApp;
        }

        console.log('WebGL context created successfully');

        // Set canvas size
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        webglApp.gl.viewport(0, 0, canvas.width, canvas.height);

        // Create shaders
        const vertexShader = webglApp.createShader(webglApp.gl.VERTEX_SHADER, webglApp.vertexShaderSource);
        const fragmentShader = webglApp.createShader(webglApp.gl.FRAGMENT_SHADER, webglApp.fragmentShaderSource);

        if (!vertexShader || !fragmentShader) {
            console.error('Failed to create shaders');
            return webglApp;
        }

        // Create program
        const program = webglApp.createProgram(vertexShader, fragmentShader);
        if (!program) {
            console.error('Failed to create program');
            return webglApp;
        }

        webglApp.gl.useProgram(program);

        // Create fullscreen quad
        const positions = new Float32Array([
            -1, -1,
             1, -1,
            -1,  1,
            -1,  1,
             1, -1,
             1,  1
        ]);

        const positionBuffer = webglApp.gl.createBuffer();
        webglApp.gl.bindBuffer(webglApp.gl.ARRAY_BUFFER, positionBuffer);
        webglApp.gl.bufferData(webglApp.gl.ARRAY_BUFFER, positions, webglApp.gl.STATIC_DRAW);

        // Get attribute and uniform locations
        const positionLocation = webglApp.gl.getAttribLocation(program, 'a_position');
        webglApp.timeLocation = webglApp.gl.getUniformLocation(program, 'u_time');
        webglApp.resolutionLocation = webglApp.gl.getUniformLocation(program, 'u_resolution');
        webglApp.textureLocation = webglApp.gl.getUniformLocation(program, 'u_texture');
        webglApp.parallaxLocation = webglApp.gl.getUniformLocation(program, 'u_parallax');

        // Enable attribute
        webglApp.gl.enableVertexAttribArray(positionLocation);
        webglApp.gl.vertexAttribPointer(positionLocation, 2, webglApp.gl.FLOAT, false, 0, 0);

        // Load mountain texture - try multiple images until one loads
        console.log('Loading mountain textures...');

        const loadMountainTextures = async () => {
            for (let i = 0; i < webglApp.mountainImages.length; i++) {
                const mountainImageUrl = webglApp.mountainImages[i];
                console.log(`Trying to load mountain texture ${i + 1}:`, mountainImageUrl);

                try {
                    const texture = await webglApp.loadTexture(mountainImageUrl);
                    webglApp.texture = texture;
                    console.log(`Mountain texture ${i + 1} loaded successfully:`, mountainImageUrl);
                    return true;
                } catch (error) {
                    console.warn(`Failed to load mountain texture ${i + 1}:`, mountainImageUrl, error);
                }
            }
            console.error('Failed to load any mountain textures');
            return false;
        };

        // Start loading textures asynchronously
        loadMountainTextures();

        // Start rendering
        webglApp.render();

        // Handle resize
        window.addEventListener('resize', () => {
            webglApp.tools.viewport.infos = {
                width: window.innerWidth,
                height: window.innerHeight
            };
        });

        return webglApp;
    }
};

// Events enum
const Events = {
    ATTACH: 'ATTACH',
    RESIZE: 'RESIZE'
};

// Export as named exports to match the imports
export { GlobalApp as G };
export { webglApp as a };
export { Events as E };
export default GlobalApp;
