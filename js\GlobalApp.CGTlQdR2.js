// GlobalApp stub
class GlobalApp {
    constructor() {
        this.state = {
            emit: (event, data) => {
                console.log('Event emitted:', event, data);
            }
        };
        this.tools = {
            viewport: {
                infos: { width: window.innerWidth, height: window.innerHeight }
            }
        };
    }

    static init(wrapper, canvas) {
        // Initialize global app functionality
        document.body.classList.add('loaded');
        console.log('GlobalApp initialized with WebGL');
        return Promise.resolve(new GlobalApp());
    }
}

// WebGL app instance with proper implementation
const webglApp = {
    canvas: null,
    gl: null,
    animationId: null,
    time: 0,

    // Interactive state
    mouse: { x: 0.5, y: 0.5 },
    scrollProgress: 0,
    currentScene: 0,
    targetScene: 0,
    sceneTransition: 0,

    // Textures and scenes
    textures: [],
    currentTexture: null,
    nextTexture: null,

    // Scene configuration - different color schemes and effects for different chapters
    scenes: [
        { name: 'Hero', colors: [0.176, 0.384, 0.549], intensity: 1.0, speed: 1.0 },
        { name: 'WhoWeAre', colors: [0.290, 0.482, 0.655], intensity: 0.8, speed: 0.7 },
        { name: 'WhatWeDo', colors: [0.549, 0.706, 0.835], intensity: 1.2, speed: 1.3 },
        { name: 'GlobalConnectivity', colors: [0.176, 0.384, 0.549], intensity: 0.9, speed: 0.8 },
        { name: 'Sustainability', colors: [0.290, 0.482, 0.655], intensity: 1.1, speed: 1.1 }
    ],

    state: {
        emit: (event, data) => {
            console.log('WebGL Event:', event, data);
        }
    },

    tools: {
        viewport: {
            infos: { width: window.innerWidth, height: window.innerHeight }
        }
    },

    // Vertex shader source
    vertexShaderSource: `
        attribute vec2 a_position;
        varying vec2 v_uv;
        void main() {
            v_uv = a_position * 0.5 + 0.5;
            gl_Position = vec4(a_position, 0.0, 1.0);
        }
    `,

    // Interactive fragment shader with sophisticated animated gradients
    fragmentShaderSource: `
        precision mediump float;
        varying vec2 v_uv;
        uniform float u_time;
        uniform vec2 u_resolution;
        uniform vec2 u_mouse;
        uniform float u_scrollProgress;
        uniform float u_sceneTransition;
        uniform vec3 u_sceneColor;
        uniform vec3 u_nextSceneColor;
        uniform float u_sceneIntensity;
        uniform float u_sceneSpeed;

        // Noise function for organic movement
        float noise(vec2 p) {
            return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
        }

        // Smooth noise
        float smoothNoise(vec2 p) {
            vec2 i = floor(p);
            vec2 f = fract(p);
            f = f * f * (3.0 - 2.0 * f);

            float a = noise(i);
            float b = noise(i + vec2(1.0, 0.0));
            float c = noise(i + vec2(0.0, 1.0));
            float d = noise(i + vec2(1.0, 1.0));

            return mix(mix(a, b, f.x), mix(c, d, f.x), f.y);
        }

        void main() {
            vec2 uv = v_uv;

            // Mouse interaction - create ripple effect around cursor
            vec2 mousePos = u_mouse;
            float mouseDistance = distance(uv, mousePos);
            float mouseEffect = 1.0 - smoothstep(0.0, 0.4, mouseDistance);
            mouseEffect *= sin(u_time * 4.0 - mouseDistance * 15.0) * 0.2 + 0.8;

            // Scene colors that transition smoothly
            vec3 currentColor = u_sceneColor;
            vec3 nextColor = u_nextSceneColor;
            vec3 sceneColor = mix(currentColor, nextColor, u_sceneTransition);

            // Create multiple wave layers for depth
            float time = u_time * u_sceneSpeed;

            // Primary wave influenced by mouse
            float wave1 = sin(uv.x * 3.14159 * 2.0 + time * 0.5 + mouseEffect * 3.0) * 0.5 + 0.5;
            float wave2 = cos(uv.y * 3.14159 * 1.5 + time * 0.3 + u_scrollProgress * 4.0) * 0.5 + 0.5;

            // Secondary waves for complexity
            float wave3 = sin((uv.x + uv.y) * 3.14159 + time * 0.7 + mouseDistance * 5.0) * 0.3 + 0.7;
            float wave4 = cos(uv.x * uv.y * 10.0 + time * 0.2) * 0.2 + 0.8;

            // Noise-based organic movement
            vec2 noiseUV = uv * 3.0 + vec2(time * 0.1, time * 0.05);
            float organicNoise = smoothNoise(noiseUV) * 0.3;

            // Combine waves with scene intensity
            float combinedWave = (wave1 * wave2 * wave3 + wave4 + organicNoise) * u_sceneIntensity;

            // Create gradient layers
            vec3 color1 = sceneColor * 0.6;  // Dark base
            vec3 color2 = sceneColor * 1.0;  // Mid tone
            vec3 color3 = sceneColor * 1.4;  // Bright highlights

            // Blend colors based on waves
            vec3 gradientColor = mix(color1, color2, wave1);
            gradientColor = mix(gradientColor, color3, wave2 * 0.4);
            gradientColor = mix(gradientColor, color2, combinedWave * 0.3);

            // Add depth and atmospheric perspective
            float depth = smoothstep(0.0, 1.0, uv.y);
            vec3 atmosphereColor = mix(sceneColor, vec3(0.8, 0.9, 1.0), 0.3);
            gradientColor = mix(gradientColor, atmosphereColor, depth * 0.15);

            // Mouse cursor glow effect
            float cursorGlow = 1.0 - smoothstep(0.0, 0.2, mouseDistance);
            cursorGlow = pow(cursorGlow, 2.0);
            gradientColor += cursorGlow * sceneColor * 0.4;

            // Add scroll-based color shifts
            float scrollEffect = sin(u_scrollProgress * 6.28) * 0.1;
            gradientColor += scrollEffect * sceneColor;

            // Subtle film grain
            float grain = noise(uv * 1000.0 + time) * 0.03;
            gradientColor += grain;

            // Ensure colors stay in valid range
            gradientColor = clamp(gradientColor, 0.0, 1.0);

            gl_FragColor = vec4(gradientColor, 1.0);
        }
    `,

    createShader: function(type, source) {
        const shader = this.gl.createShader(type);
        this.gl.shaderSource(shader, source);
        this.gl.compileShader(shader);

        if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
            console.error('Shader compilation error:', this.gl.getShaderInfoLog(shader));
            this.gl.deleteShader(shader);
            return null;
        }

        return shader;
    },

    createProgram: function(vertexShader, fragmentShader) {
        const program = this.gl.createProgram();
        this.gl.attachShader(program, vertexShader);
        this.gl.attachShader(program, fragmentShader);
        this.gl.linkProgram(program);

        if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
            console.error('Program linking error:', this.gl.getProgramInfoLog(program));
            this.gl.deleteProgram(program);
            return null;
        }

        return program;
    },

    setupInteraction: function() {
        // Mouse tracking
        document.addEventListener('mousemove', (e) => {
            this.mouse.x = e.clientX / window.innerWidth;
            this.mouse.y = 1.0 - (e.clientY / window.innerHeight); // Flip Y for WebGL
        });

        // Scroll tracking with scene detection
        window.addEventListener('scroll', () => {
            const scrollY = window.scrollY;
            const maxScroll = document.body.scrollHeight - window.innerHeight;
            this.scrollProgress = Math.min(scrollY / Math.max(maxScroll, 1), 1.0);

            // Detect current chapter/scene based on scroll position
            const chapters = document.querySelectorAll('[data-chapter]');
            let newScene = 0;

            chapters.forEach((chapter, index) => {
                const rect = chapter.getBoundingClientRect();
                const isVisible = rect.top < window.innerHeight * 0.5 && rect.bottom > window.innerHeight * 0.5;
                if (isVisible && index < this.scenes.length) {
                    newScene = index;
                }
            });

            // Trigger scene transition if scene changed
            if (newScene !== this.targetScene) {
                this.targetScene = newScene;
                console.log(`Scene transition to: ${this.scenes[newScene]?.name || 'Unknown'}`);
            }
        });

        // Cursor interaction effects
        document.addEventListener('mousedown', () => {
            document.body.style.cursor = 'grabbing';
        });

        document.addEventListener('mouseup', () => {
            document.body.style.cursor = 'grab';
        });

        console.log('Interactive controls initialized');
    },



    render: function() {
        this.time += 0.016; // ~60fps

        // Resize canvas if needed
        if (this.canvas.width !== window.innerWidth || this.canvas.height !== window.innerHeight) {
            this.canvas.width = window.innerWidth;
            this.canvas.height = window.innerHeight;
            this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
        }

        // Update scene transition
        if (this.currentScene !== this.targetScene) {
            this.sceneTransition += 0.02; // Smooth transition speed
            if (this.sceneTransition >= 1.0) {
                this.currentScene = this.targetScene;
                this.sceneTransition = 0.0;

                // Update textures for next transition
                if (this.textures[this.currentScene]) {
                    this.currentTexture = this.textures[this.currentScene];
                }
            }
        } else {
            this.sceneTransition = Math.max(0.0, this.sceneTransition - 0.01);
        }

        // Clear and draw
        this.gl.clear(this.gl.COLOR_BUFFER_BIT);

        // Update basic uniforms
        this.gl.uniform1f(this.timeLocation, this.time);
        this.gl.uniform2f(this.resolutionLocation, this.canvas.width, this.canvas.height);

        // Update interactive uniforms
        this.gl.uniform2f(this.mouseLocation, this.mouse.x, this.mouse.y);
        this.gl.uniform1f(this.scrollProgressLocation, this.scrollProgress);
        this.gl.uniform1f(this.sceneTransitionLocation, this.sceneTransition);

        // Update scene colors and properties
        const currentScene = this.scenes[this.currentScene] || this.scenes[0];
        const nextScene = this.scenes[this.targetScene] || this.scenes[0];
        this.gl.uniform3f(this.sceneColorLocation, ...currentScene.colors);
        this.gl.uniform3f(this.nextSceneColorLocation, ...nextScene.colors);
        this.gl.uniform1f(this.sceneIntensityLocation, currentScene.intensity);
        this.gl.uniform1f(this.sceneSpeedLocation, currentScene.speed);

        // Draw
        this.gl.drawArrays(this.gl.TRIANGLES, 0, 6);

        // Continue animation
        this.animationId = requestAnimationFrame(() => this.render());
    },

    init: async (wrapper, canvas) => {
        console.log('WebGL initialized with wrapper:', wrapper, 'canvas:', canvas);

        if (!canvas) {
            console.error('No canvas provided');
            return webglApp;
        }

        webglApp.canvas = canvas;
        webglApp.gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

        if (!webglApp.gl) {
            console.log('WebGL not supported, using CSS fallback');
            if (wrapper) {
                wrapper.style.background = 'linear-gradient(135deg, #2d628c 0%, #4a7ba7 100%)';
            }
            return webglApp;
        }

        console.log('WebGL context created successfully');

        // Set canvas size
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        webglApp.gl.viewport(0, 0, canvas.width, canvas.height);

        // Create shaders
        const vertexShader = webglApp.createShader(webglApp.gl.VERTEX_SHADER, webglApp.vertexShaderSource);
        const fragmentShader = webglApp.createShader(webglApp.gl.FRAGMENT_SHADER, webglApp.fragmentShaderSource);

        if (!vertexShader || !fragmentShader) {
            console.error('Failed to create shaders');
            return webglApp;
        }

        // Create program
        const program = webglApp.createProgram(vertexShader, fragmentShader);
        if (!program) {
            console.error('Failed to create program');
            return webglApp;
        }

        webglApp.gl.useProgram(program);

        // Create fullscreen quad
        const positions = new Float32Array([
            -1, -1,
             1, -1,
            -1,  1,
            -1,  1,
             1, -1,
             1,  1
        ]);

        const positionBuffer = webglApp.gl.createBuffer();
        webglApp.gl.bindBuffer(webglApp.gl.ARRAY_BUFFER, positionBuffer);
        webglApp.gl.bufferData(webglApp.gl.ARRAY_BUFFER, positions, webglApp.gl.STATIC_DRAW);

        // Get attribute and uniform locations
        const positionLocation = webglApp.gl.getAttribLocation(program, 'a_position');
        webglApp.timeLocation = webglApp.gl.getUniformLocation(program, 'u_time');
        webglApp.resolutionLocation = webglApp.gl.getUniformLocation(program, 'u_resolution');
        webglApp.mouseLocation = webglApp.gl.getUniformLocation(program, 'u_mouse');
        webglApp.scrollProgressLocation = webglApp.gl.getUniformLocation(program, 'u_scrollProgress');
        webglApp.sceneTransitionLocation = webglApp.gl.getUniformLocation(program, 'u_sceneTransition');
        webglApp.sceneColorLocation = webglApp.gl.getUniformLocation(program, 'u_sceneColor');
        webglApp.nextSceneColorLocation = webglApp.gl.getUniformLocation(program, 'u_nextSceneColor');
        webglApp.sceneIntensityLocation = webglApp.gl.getUniformLocation(program, 'u_sceneIntensity');
        webglApp.sceneSpeedLocation = webglApp.gl.getUniformLocation(program, 'u_sceneSpeed');

        // Enable attribute
        webglApp.gl.enableVertexAttribArray(positionLocation);
        webglApp.gl.vertexAttribPointer(positionLocation, 2, webglApp.gl.FLOAT, false, 0, 0);

        // Initialize scene system
        console.log('Initializing interactive gradient scenes...');
        console.log(`Configured ${webglApp.scenes.length} scenes:`, webglApp.scenes.map(s => s.name).join(', '));

        // Setup interactive controls
        webglApp.setupInteraction();

        // Start rendering
        webglApp.render();

        // Handle resize
        window.addEventListener('resize', () => {
            webglApp.tools.viewport.infos = {
                width: window.innerWidth,
                height: window.innerHeight
            };
        });

        console.log('Interactive WebGL mountain background initialized');
        return webglApp;
    }
};

// Events enum
const Events = {
    ATTACH: 'ATTACH',
    RESIZE: 'RESIZE'
};

// Export as named exports to match the imports
export { GlobalApp as G };
export { webglApp as a };
export { Events as E };
export default GlobalApp;
