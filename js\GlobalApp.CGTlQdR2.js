// GlobalApp stub
class GlobalApp {
    constructor() {
        this.state = {
            emit: (event, data) => {
                console.log('Event emitted:', event, data);
            }
        };
        this.tools = {
            viewport: {
                infos: { width: window.innerWidth, height: window.innerHeight }
            }
        };
    }

    static init(wrapper, canvas) {
        // Initialize global app functionality
        document.body.classList.add('loaded');
        console.log('GlobalApp initialized with WebGL');
        return Promise.resolve(new GlobalApp());
    }
}

// WebGL app instance
const webglApp = {
    state: {
        emit: (event, data) => {
            console.log('WebGL Event:', event, data);
        }
    },
    tools: {
        viewport: {
            infos: { width: window.innerWidth, height: window.innerHeight }
        }
    },
    init: async (wrapper, canvas) => {
        console.log('WebGL initialized');
        return webglApp;
    }
};

// Events enum
const Events = {
    ATTACH: 'ATTACH',
    RESIZE: 'RESIZE'
};

// Export as named exports to match the imports
export { GlobalApp as G };
export { webglApp as a };
export { Events as E };
export default GlobalApp;
