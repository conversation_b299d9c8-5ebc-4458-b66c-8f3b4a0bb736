<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Technology Animation Demo</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 0;
            overflow-x: hidden;
            background-color: #000;
            scroll-behavior: smooth;
            color: #00ff88;
        }

        /* Fixed Scene Container */
        .tech-scene-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            z-index: -1;
        }

        /* Neural Network Background */
        .neural-network-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 400vh;
            background: linear-gradient(
                45deg,
                #0a0a0f 0%,
                #1a0a2e 25%,
                #2d1b69 50%,
                #8b5cf6 75%,
                #c084fc 100%
            );
            transform: translateY(0);
            transition: all 0.1s ease-out;
        }

        /* Circuit Board Pattern */
        .circuit-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(90deg, transparent 98%, rgba(0, 255, 136, 0.3) 100%),
                linear-gradient(0deg, transparent 98%, rgba(0, 255, 136, 0.3) 100%);
            background-size: 50px 50px;
            opacity: 0.3;
            z-index: 5;
            animation: circuit-pulse 4s ease-in-out infinite;
        }

        @keyframes circuit-pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }

        /* Holographic Grid */
        .holographic-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                linear-gradient(90deg, transparent 49%, rgba(0, 255, 255, 0.1) 50%, transparent 51%),
                linear-gradient(0deg, transparent 49%, rgba(0, 255, 255, 0.1) 50%, transparent 51%);
            background-size: 100px 100px;
            z-index: 10;
            opacity: 0;
            transition: opacity 1s ease;
        }

        /* Data Stream Lines */
        .data-stream {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, 
                transparent 0%, 
                #00ff88 20%, 
                #00ffff 50%, 
                #8b5cf6 80%, 
                transparent 100%);
            animation: data-flow linear infinite;
            opacity: 0.8;
            z-index: 15;
        }

        .data-stream:nth-child(1) {
            top: 20%;
            width: 300px;
            animation-duration: 8s;
            animation-delay: 0s;
        }

        .data-stream:nth-child(2) {
            top: 40%;
            width: 400px;
            animation-duration: 12s;
            animation-delay: 2s;
        }

        .data-stream:nth-child(3) {
            top: 60%;
            width: 250px;
            animation-duration: 10s;
            animation-delay: 4s;
        }

        .data-stream:nth-child(4) {
            top: 80%;
            width: 350px;
            animation-duration: 15s;
            animation-delay: 1s;
        }

        @keyframes data-flow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100vw); }
        }

        /* Neural Network Nodes */
        .neural-nodes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 20;
            opacity: 0;
            transition: opacity 1s ease;
        }

        .node {
            position: absolute;
            width: 8px;
            height: 8px;
            background: radial-gradient(circle, #00ff88, transparent);
            border-radius: 50%;
            animation: node-pulse 3s ease-in-out infinite;
        }

        .node::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            width: 16px;
            height: 16px;
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 50%;
            animation: node-ring 3s ease-in-out infinite;
        }

        @keyframes node-pulse {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.5); }
        }

        @keyframes node-ring {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(2); opacity: 0; }
        }

        /* Connection Lines */
        .connection-line {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, 
                transparent 0%, 
                rgba(0, 255, 136, 0.6) 50%, 
                transparent 100%);
            transform-origin: left center;
            animation: connection-pulse 4s ease-in-out infinite;
        }

        @keyframes connection-pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }

        /* Floating Code Particles */
        .code-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 25;
            opacity: 0;
            transition: opacity 1s ease;
        }

        .code-particle {
            position: absolute;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: rgba(0, 255, 136, 0.7);
            animation: float-code linear infinite;
            pointer-events: none;
        }

        @keyframes float-code {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Glitch Effects */
        .glitch-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 30;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .glitch-bar {
            position: absolute;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, 
                transparent 0%, 
                #ff0080 20%, 
                #00ffff 50%, 
                #ff0080 80%, 
                transparent 100%);
            animation: glitch-scan 3s linear infinite;
        }

        @keyframes glitch-scan {
            0% { top: -2px; opacity: 1; }
            100% { top: 100vh; opacity: 0; }
        }

        /* Content Sections */
        .content-section {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 100;
            padding: 4rem 2rem;
            text-align: center;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(0, 255, 136, 0.2);
            margin: 2rem;
            border-radius: 10px;
        }

        .content-section h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 0 0 20px #00ff88;
            animation: text-glow 2s ease-in-out infinite alternate;
        }

        .content-section h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #8b5cf6;
            text-shadow: 0 0 15px #8b5cf6;
        }

        .content-section p {
            font-size: 1.2rem;
            line-height: 1.6;
            max-width: 800px;
            color: rgba(0, 255, 136, 0.9);
        }

        @keyframes text-glow {
            0% { text-shadow: 0 0 20px #00ff88; }
            100% { text-shadow: 0 0 30px #00ff88, 0 0 40px #00ff88; }
        }

        .tech-button {
            background: linear-gradient(45deg, #8b5cf6, #00ff88);
            color: #000;
            border: none;
            padding: 1rem 2rem;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            text-transform: uppercase;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 2rem;
        }

        .tech-button:hover {
            transform: scale(1.05);
            box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
        }
    </style>
</head>
<body>
    <!-- Fixed Technology Scene Container -->
    <div class="tech-scene-container">
        <!-- Neural Network Background -->
        <div class="neural-network-bg" id="neuralBg"></div>
        
        <!-- Circuit Board Layer -->
        <div class="circuit-layer"></div>
        
        <!-- Holographic Grid -->
        <div class="holographic-grid" id="holoGrid"></div>
        
        <!-- Data Streams -->
        <div class="data-streams" id="dataStreams">
            <div class="data-stream"></div>
            <div class="data-stream"></div>
            <div class="data-stream"></div>
            <div class="data-stream"></div>
        </div>
        
        <!-- Neural Network Nodes -->
        <div class="neural-nodes" id="neuralNodes"></div>
        
        <!-- Code Particles -->
        <div class="code-particles" id="codeParticles"></div>
        
        <!-- Glitch Effects -->
        <div class="glitch-layer" id="glitchLayer">
            <div class="glitch-bar"></div>
        </div>
    </div>

    <!-- Content Sections -->
    <section class="content-section">
        <h1>NEURAL NETWORKS</h1>
        <p>Experience the power of artificial intelligence as neural networks process information in real-time. Watch as data flows through interconnected nodes, creating patterns of digital consciousness.</p>
        <button class="tech-button">Initialize AI</button>
    </section>

    <section class="content-section">
        <h2>DATA PROCESSING</h2>
        <p>Witness the flow of information through advanced data streams. Each pulse represents millions of calculations, transforming raw data into actionable intelligence through sophisticated algorithms.</p>
        <button class="tech-button">Process Data</button>
    </section>

    <section class="content-section">
        <h2>QUANTUM COMPUTING</h2>
        <p>Enter the realm of quantum mechanics where traditional computing boundaries dissolve. Observe quantum states and superposition as they revolutionize computational possibilities.</p>
        <button class="tech-button">Quantum Leap</button>
    </section>

    <section class="content-section">
        <h2>DIGITAL FUTURE</h2>
        <p>Step into tomorrow's technology landscape where holographic interfaces and advanced AI converge to create unprecedented digital experiences and human-machine collaboration.</p>
        <button class="tech-button">Enter Future</button>
    </section>

    <script>
        class TechAnimationController {
            constructor() {
                this.neuralBg = document.getElementById('neuralBg');
                this.holoGrid = document.getElementById('holoGrid');
                this.neuralNodes = document.getElementById('neuralNodes');
                this.codeParticles = document.getElementById('codeParticles');
                this.glitchLayer = document.getElementById('glitchLayer');

                this.init();
            }

            init() {
                this.createNeuralNodes();
                this.createCodeParticles();
                this.setupScrollAnimation();

                // Initial animation state
                this.updateAnimation();
            }

            createNeuralNodes() {
                const nodeCount = 20;
                const container = this.neuralNodes;

                for (let i = 0; i < nodeCount; i++) {
                    const node = document.createElement('div');
                    node.className = 'node';

                    // Random positioning
                    node.style.left = Math.random() * 100 + '%';
                    node.style.top = Math.random() * 100 + '%';
                    node.style.animationDelay = Math.random() * 3 + 's';

                    container.appendChild(node);

                    // Create connection lines
                    if (i > 0) {
                        this.createConnectionLine(container.children[i-1], node);
                    }
                }
            }

            createConnectionLine(node1, node2) {
                const line = document.createElement('div');
                line.className = 'connection-line';

                const rect1 = node1.getBoundingClientRect();
                const rect2 = node2.getBoundingClientRect();

                const distance = Math.sqrt(
                    Math.pow(rect2.left - rect1.left, 2) +
                    Math.pow(rect2.top - rect1.top, 2)
                );

                const angle = Math.atan2(
                    rect2.top - rect1.top,
                    rect2.left - rect1.left
                ) * 180 / Math.PI;

                line.style.width = distance + 'px';
                line.style.left = rect1.left + 'px';
                line.style.top = rect1.top + 'px';
                line.style.transform = `rotate(${angle}deg)`;
                line.style.animationDelay = Math.random() * 4 + 's';

                this.neuralNodes.appendChild(line);
            }

            createCodeParticles() {
                const codeSnippets = [
                    'function()', 'if(true)', 'for(i=0)', 'return x;',
                    'class AI', 'import *', 'async/await', 'const data',
                    '01101001', '11010011', 'neural.net', 'quantum.bit',
                    'AI.learn()', 'data.flow', 'node.js', 'python.ai'
                ];

                setInterval(() => {
                    const particle = document.createElement('div');
                    particle.className = 'code-particle';
                    particle.textContent = codeSnippets[Math.floor(Math.random() * codeSnippets.length)];

                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.animationDuration = (10 + Math.random() * 10) + 's';
                    particle.style.animationDelay = Math.random() * 2 + 's';

                    this.codeParticles.appendChild(particle);

                    // Remove particle after animation
                    setTimeout(() => {
                        if (particle.parentNode) {
                            particle.parentNode.removeChild(particle);
                        }
                    }, 20000);
                }, 2000);
            }

            setupScrollAnimation() {
                window.addEventListener('scroll', () => {
                    this.updateAnimation();
                });
            }

            updateAnimation() {
                const scrollY = window.scrollY;
                const totalHeight = document.body.scrollHeight - window.innerHeight;
                const scrollProgress = Math.min(scrollY / totalHeight, 1);

                // Neural network background movement
                const bgMovement = scrollProgress * (this.neuralBg.offsetHeight - window.innerHeight);
                this.neuralBg.style.transform = `translateY(-${bgMovement}px)`;

                // Phase-based animations
                this.updatePhaseEffects(scrollProgress);
            }

            updatePhaseEffects(progress) {
                // Phase 1: Neural Network (0-25%)
                if (progress <= 0.25) {
                    const phaseProgress = progress / 0.25;
                    this.neuralNodes.style.opacity = phaseProgress;
                    this.holoGrid.style.opacity = 0;
                    this.codeParticles.style.opacity = 0;
                    this.glitchLayer.style.opacity = 0;
                }
                // Phase 2: Data Processing (25-50%)
                else if (progress <= 0.5) {
                    const phaseProgress = (progress - 0.25) / 0.25;
                    this.neuralNodes.style.opacity = 1 - phaseProgress * 0.5;
                    this.holoGrid.style.opacity = phaseProgress;
                    this.codeParticles.style.opacity = phaseProgress;
                    this.glitchLayer.style.opacity = 0;
                }
                // Phase 3: Quantum Computing (50-75%)
                else if (progress <= 0.75) {
                    const phaseProgress = (progress - 0.5) / 0.25;
                    this.neuralNodes.style.opacity = 0.5 + phaseProgress * 0.5;
                    this.holoGrid.style.opacity = 1;
                    this.codeParticles.style.opacity = 1;
                    this.glitchLayer.style.opacity = phaseProgress * 0.3;
                }
                // Phase 4: Digital Future (75-100%)
                else {
                    const phaseProgress = (progress - 0.75) / 0.25;
                    this.neuralNodes.style.opacity = 1;
                    this.holoGrid.style.opacity = 1;
                    this.codeParticles.style.opacity = 1;
                    this.glitchLayer.style.opacity = 0.3 + phaseProgress * 0.7;
                }
            }
        }

        // Initialize the animation system
        document.addEventListener('DOMContentLoaded', () => {
            new TechAnimationController();
            console.log('Advanced Technology Animation System Initialized');
        });
    </script>
</body>
</html>
