/* Visibility Fix for Navigation and Text Elements */

/* Ensure body and main content are visible */
body {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Fix navigation visibility */
header, nav, .nav-link {
    opacity: 1 !important;
    visibility: visible !important;
    color: #2d628c !important;
}

/* Fix menu text visibility */
.menu-links-w, .menu-links-w ul, .menu-links-w li, .menu-links-w a {
    opacity: 1 !important;
    visibility: visible !important;
    color: #2d628c !important;
}

/* Fix main content text visibility */
main, section, .hero, .hero-inner {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Fix logo visibility */
.logo {
    opacity: 1 !important;
    visibility: visible !important;
    fill: #2d628c !important;
}

/* Fix all text elements */
h1, h2, h3, h4, h5, h6, p, span, a, div {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Fix specific navigation elements */
.nav-link span {
    color: #2d628c !important;
    opacity: 1 !important;
}

/* Fix news and menu buttons */
.news-w, .menu-cta {
    opacity: 1 !important;
    visibility: visible !important;
    color: #2d628c !important;
}

/* Fix hero transition overlay that might be hiding content */
.hero-transition {
    opacity: 1 !important;
    pointer-events: auto !important;
}

/* Fix any hidden text in hero sections */
.hero-transition .title p {
    opacity: 1 !important;
    visibility: visible !important;
    color: #2d628c !important;
}

/* Fix scroll-to-cta visibility */
.scroll-to-cta {
    opacity: 1 !important;
    visibility: visible !important;
    color: #2d628c !important;
}

/* Fix chapters navigation */
.chapters-navigation {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Fix any text that might be transformed off-screen */
.text-container span {
    transform: translate3d(0, 0, 0) !important;
    opacity: 1 !important;
}

/* Fix menu overlay */
.montfort-menu {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Fix any elements with transforms that hide them */
[data-astro-cid] {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ensure all interactive elements are visible */
button, .button, .btn {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Fix any CSS animations that might hide content */
* {
    animation-play-state: paused !important;
}

/* Override any view transition styles that might hide content */
[data-astro-transition-scope] {
    opacity: 1 !important;
    visibility: visible !important;
}
