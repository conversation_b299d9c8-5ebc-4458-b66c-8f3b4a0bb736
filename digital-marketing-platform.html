<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MarketFlow Pro - Revolutionary Digital Marketing Platform</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #0a0a0f;
            color: white;
            overflow-x: hidden;
            cursor: none;
        }

        /* Custom Marketing Cursor */
        .marketing-cursor {
            position: fixed;
            width: 24px;
            height: 24px;
            background: radial-gradient(circle, #ff6b6b, #4ecdc4, #45b7d1);
            border-radius: 50%;
            pointer-events: none;
            z-index: 10000;
            mix-blend-mode: difference;
            transition: transform 0.1s ease;
            animation: cursor-pulse 2s ease-in-out infinite;
        }

        @keyframes cursor-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }

        .cursor-trail {
            position: fixed;
            width: 6px;
            height: 6px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            opacity: 0;
        }

        /* Fixed Marketing Scene Container */
        .marketing-scene {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: -1;
            overflow: hidden;
        }

        /* Animated Background Gradient */
        .gradient-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 500vh;
            background: linear-gradient(
                45deg,
                #0a0a0f 0%,
                #1a1a2e 20%,
                #16213e 40%,
                #0f3460 60%,
                #533483 80%,
                #7209b7 100%
            );
            transform: translateY(0);
            transition: transform 0.1s ease-out;
        }

        /* Data Flow Streams */
        .data-streams {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10;
        }

        .data-stream {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, 
                transparent 0%, 
                #ff6b6b 20%, 
                #4ecdc4 50%, 
                #45b7d1 80%, 
                transparent 100%);
            animation: data-flow linear infinite;
            opacity: 0.8;
        }

        .data-stream:nth-child(1) {
            top: 15%;
            width: 400px;
            animation-duration: 6s;
            animation-delay: 0s;
        }

        .data-stream:nth-child(2) {
            top: 35%;
            width: 300px;
            animation-duration: 8s;
            animation-delay: 1s;
        }

        .data-stream:nth-child(3) {
            top: 55%;
            width: 500px;
            animation-duration: 7s;
            animation-delay: 2s;
        }

        .data-stream:nth-child(4) {
            top: 75%;
            width: 350px;
            animation-duration: 9s;
            animation-delay: 0.5s;
        }

        @keyframes data-flow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100vw); }
        }

        /* Floating Analytics Charts */
        .analytics-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 15;
            opacity: 0;
            transition: opacity 1s ease;
        }

        .floating-chart {
            position: absolute;
            width: 120px;
            height: 80px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 10px;
            animation: float-chart 8s ease-in-out infinite;
        }

        .floating-chart:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-chart:nth-child(2) {
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .floating-chart:nth-child(3) {
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float-chart {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(2deg); }
        }

        /* Social Media Icons */
        .social-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 20;
            opacity: 0;
            transition: opacity 1s ease;
        }

        .social-icon {
            position: absolute;
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            animation: social-float 6s ease-in-out infinite;
            box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
        }

        @keyframes social-float {
            0%, 100% { transform: translateY(0) scale(1); }
            50% { transform: translateY(-30px) scale(1.1); }
        }

        /* Growth Arrow Animations */
        .growth-arrows {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 25;
            opacity: 0;
            transition: opacity 1s ease;
        }

        .growth-arrow {
            position: absolute;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
            animation: growth-rise 4s ease-in-out infinite;
            filter: drop-shadow(0 0 10px rgba(78, 205, 196, 0.6));
        }

        @keyframes growth-rise {
            0% { transform: translateY(50px) scale(0.8); opacity: 0; }
            50% { transform: translateY(-20px) scale(1.2); opacity: 1; }
            100% { transform: translateY(-100px) scale(0.8); opacity: 0; }
        }

        /* ROI Counter Animation */
        .roi-display {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 30;
            opacity: 0;
            transition: opacity 1s ease;
        }

        .roi-counter {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(15px);
            border: 2px solid #4ecdc4;
            border-radius: 12px;
            padding: 15px 20px;
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            font-size: 18px;
            color: #4ecdc4;
            animation: roi-pulse 3s ease-in-out infinite;
        }

        @keyframes roi-pulse {
            0%, 100% { transform: scale(1); box-shadow: 0 0 20px rgba(78, 205, 196, 0.3); }
            50% { transform: scale(1.05); box-shadow: 0 0 30px rgba(78, 205, 196, 0.6); }
        }

        /* Content Sections */
        .content-section {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 100;
            padding: 4rem 2rem;
            text-align: center;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin: 2rem;
            border-radius: 20px;
            transition: all 0.5s ease;
        }

        .content-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 20px;
            padding: 2px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #7209b7);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            animation: border-flow 4s linear infinite;
        }

        @keyframes border-flow {
            0% { background-position: 0% 50%; }
            100% { background-position: 100% 50%; }
        }

        .content-section:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 25px 50px rgba(255, 107, 107, 0.2);
        }

        .content-section h1 {
            font-size: 4.5rem;
            font-weight: 900;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2rem;
            animation: text-glow 3s ease-in-out infinite;
        }

        .content-section h2 {
            font-size: 3.5rem;
            font-weight: 800;
            color: #4ecdc4;
            margin-bottom: 1.5rem;
            text-shadow: 0 0 30px rgba(78, 205, 196, 0.5);
        }

        .content-section p {
            font-size: 1.4rem;
            line-height: 1.8;
            color: rgba(255, 255, 255, 0.9);
            max-width: 900px;
            margin-bottom: 2.5rem;
        }

        @keyframes text-glow {
            0%, 100% { filter: brightness(1); }
            50% { filter: brightness(1.2) drop-shadow(0 0 20px rgba(255, 107, 107, 0.5)); }
        }

        .marketing-button {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            color: white;
            padding: 1.2rem 2.5rem;
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            font-size: 1.1rem;
            text-transform: uppercase;
            border-radius: 50px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            letter-spacing: 0.5px;
        }

        .marketing-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        .marketing-button:hover::before {
            left: 100%;
        }

        .marketing-button:hover {
            transform: scale(1.05) translateY(-2px);
            box-shadow: 0 15px 30px rgba(255, 107, 107, 0.4);
        }
    </style>
</head>
<body>
    <!-- Custom Marketing Cursor -->
    <div class="marketing-cursor" id="marketingCursor"></div>

    <!-- Fixed Marketing Scene Container -->
    <div class="marketing-scene">
        <!-- Animated Background -->
        <div class="gradient-bg" id="gradientBg"></div>
        
        <!-- Data Flow Streams -->
        <div class="data-streams">
            <div class="data-stream"></div>
            <div class="data-stream"></div>
            <div class="data-stream"></div>
            <div class="data-stream"></div>
        </div>
        
        <!-- Analytics Layer -->
        <div class="analytics-layer" id="analyticsLayer">
            <div class="floating-chart"></div>
            <div class="floating-chart"></div>
            <div class="floating-chart"></div>
        </div>
        
        <!-- Social Media Particles -->
        <div class="social-particles" id="socialParticles"></div>
        
        <!-- Growth Arrows -->
        <div class="growth-arrows" id="growthArrows"></div>
        
        <!-- ROI Display -->
        <div class="roi-display" id="roiDisplay"></div>
    </div>

    <!-- Content Sections -->
    <section class="content-section">
        <h1>MarketFlow Pro</h1>
        <p>Revolutionary AI-powered digital marketing platform that transforms data into growth. Watch your campaigns come alive with real-time analytics, automated optimization, and predictive insights that drive unprecedented ROI.</p>
        <button class="marketing-button">Start Your Growth Journey</button>
    </section>

    <section class="content-section">
        <h2>Data-Driven Intelligence</h2>
        <p>Harness the power of advanced analytics and machine learning to understand your audience like never before. Our platform processes millions of data points in real-time, revealing hidden patterns and opportunities that fuel exponential growth.</p>
        <button class="marketing-button">Unlock Insights</button>
    </section>

    <section class="content-section">
        <h2>Social Media Mastery</h2>
        <p>Dominate every social platform with our intelligent automation engine. From content creation to audience targeting, watch as our AI orchestrates campaigns across all channels, maximizing engagement and converting followers into customers.</p>
        <button class="marketing-button">Amplify Your Reach</button>
    </section>

    <section class="content-section">
        <h2>ROI Acceleration</h2>
        <p>Experience explosive growth with our proven optimization algorithms. Our platform continuously learns and adapts, automatically adjusting campaigns to maximize return on investment while minimizing costs and effort.</p>
        <button class="marketing-button">Maximize Returns</button>
    </section>

    <script>
        class MarketingPlatformAnimation {
            constructor() {
                this.gradientBg = document.getElementById('gradientBg');
                this.analyticsLayer = document.getElementById('analyticsLayer');
                this.socialParticles = document.getElementById('socialParticles');
                this.growthArrows = document.getElementById('growthArrows');
                this.roiDisplay = document.getElementById('roiDisplay');

                this.mouseX = 0;
                this.mouseY = 0;
                this.scrollProgress = 0;

                this.init();
            }

            init() {
                this.createSocialIcons();
                this.createGrowthArrows();
                this.createROICounters();
                this.setupCursor();
                this.setupScrollAnimation();
                this.createFloatingCharts();
                this.animate();
            }

            createSocialIcons() {
                const socialIcons = ['📱', '💬', '📊', '🎯', '💡', '🚀', '📈', '⭐'];
                const container = this.socialParticles;

                socialIcons.forEach((icon, index) => {
                    const socialIcon = document.createElement('div');
                    socialIcon.className = 'social-icon';
                    socialIcon.textContent = icon;

                    socialIcon.style.left = Math.random() * 90 + '%';
                    socialIcon.style.top = Math.random() * 80 + '%';
                    socialIcon.style.animationDelay = Math.random() * 6 + 's';
                    socialIcon.style.animationDuration = (6 + Math.random() * 4) + 's';

                    container.appendChild(socialIcon);
                });
            }

            createGrowthArrows() {
                const container = this.growthArrows;

                for (let i = 0; i < 8; i++) {
                    const arrow = document.createElement('div');
                    arrow.className = 'growth-arrow';

                    arrow.style.left = Math.random() * 90 + '%';
                    arrow.style.top = Math.random() * 80 + 20 + '%';
                    arrow.style.animationDelay = Math.random() * 4 + 's';
                    arrow.style.animationDuration = (4 + Math.random() * 2) + 's';

                    container.appendChild(arrow);
                }
            }

            createROICounters() {
                const roiData = [
                    { label: 'ROI Increase', value: '347%', x: '15%', y: '20%' },
                    { label: 'Conversion Rate', value: '+89%', x: '70%', y: '30%' },
                    { label: 'Cost Reduction', value: '-52%', x: '25%', y: '70%' },
                    { label: 'Revenue Growth', value: '+234%', x: '80%', y: '65%' }
                ];

                const container = this.roiDisplay;

                roiData.forEach((data, index) => {
                    const counter = document.createElement('div');
                    counter.className = 'roi-counter';
                    counter.innerHTML = `
                        <div style="font-size: 14px; opacity: 0.8;">${data.label}</div>
                        <div style="font-size: 24px; margin-top: 5px;">${data.value}</div>
                    `;

                    counter.style.left = data.x;
                    counter.style.top = data.y;
                    counter.style.animationDelay = index * 0.5 + 's';

                    container.appendChild(counter);
                });
            }

            createFloatingCharts() {
                // Create mini chart visualizations
                const charts = document.querySelectorAll('.floating-chart');

                charts.forEach((chart, index) => {
                    const canvas = document.createElement('canvas');
                    canvas.width = 100;
                    canvas.height = 60;
                    chart.appendChild(canvas);

                    const ctx = canvas.getContext('2d');
                    this.drawMiniChart(ctx, index);
                });
            }

            drawMiniChart(ctx, type) {
                ctx.clearRect(0, 0, 100, 60);
                ctx.strokeStyle = '#4ecdc4';
                ctx.lineWidth = 2;

                if (type === 0) {
                    // Line chart
                    ctx.beginPath();
                    const points = [10, 45, 25, 30, 40, 35, 55, 20, 70, 15, 85, 10];
                    ctx.moveTo(5, points[0]);
                    for (let i = 1; i < points.length; i += 2) {
                        ctx.lineTo(points[i], points[i + 1]);
                    }
                    ctx.stroke();
                } else if (type === 1) {
                    // Bar chart
                    const bars = [20, 35, 45, 30, 50];
                    bars.forEach((height, i) => {
                        ctx.fillStyle = '#4ecdc4';
                        ctx.fillRect(i * 18 + 5, 60 - height, 15, height);
                    });
                } else {
                    // Pie chart
                    const centerX = 50;
                    const centerY = 30;
                    const radius = 20;

                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius, 0, Math.PI);
                    ctx.fillStyle = '#4ecdc4';
                    ctx.fill();

                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius, Math.PI, 2 * Math.PI);
                    ctx.fillStyle = '#ff6b6b';
                    ctx.fill();
                }
            }

            setupCursor() {
                const cursor = document.getElementById('marketingCursor');
                const trails = [];

                // Create cursor trails
                for (let i = 0; i < 8; i++) {
                    const trail = document.createElement('div');
                    trail.className = 'cursor-trail';
                    document.body.appendChild(trail);
                    trails.push(trail);
                }

                document.addEventListener('mousemove', (e) => {
                    this.mouseX = e.clientX;
                    this.mouseY = e.clientY;

                    cursor.style.left = e.clientX + 'px';
                    cursor.style.top = e.clientY + 'px';

                    // Update cursor trails
                    trails.forEach((trail, index) => {
                        setTimeout(() => {
                            trail.style.left = e.clientX + 'px';
                            trail.style.top = e.clientY + 'px';
                            trail.style.opacity = (8 - index) / 8 * 0.6;
                        }, index * 30);
                    });
                });

                // Click effects
                document.addEventListener('click', (e) => {
                    this.createClickRipple(e.clientX, e.clientY);
                });
            }

            createClickRipple(x, y) {
                const ripple = document.createElement('div');
                ripple.style.position = 'fixed';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.style.width = '10px';
                ripple.style.height = '10px';
                ripple.style.background = 'radial-gradient(circle, #ff6b6b, transparent)';
                ripple.style.borderRadius = '50%';
                ripple.style.pointerEvents = 'none';
                ripple.style.zIndex = '10001';
                ripple.style.transform = 'translate(-50%, -50%)';

                document.body.appendChild(ripple);

                ripple.animate([
                    { transform: 'translate(-50%, -50%) scale(1)', opacity: 1 },
                    { transform: 'translate(-50%, -50%) scale(20)', opacity: 0 }
                ], {
                    duration: 800,
                    easing: 'ease-out'
                }).onfinish = () => ripple.remove();
            }

            setupScrollAnimation() {
                window.addEventListener('scroll', () => {
                    const scrollY = window.scrollY;
                    const totalHeight = document.body.scrollHeight - window.innerHeight;
                    this.scrollProgress = Math.min(scrollY / totalHeight, 1);

                    this.updateAnimationPhases();
                });
            }

            updateAnimationPhases() {
                // Background gradient movement
                const bgMovement = this.scrollProgress * (this.gradientBg.offsetHeight - window.innerHeight);
                this.gradientBg.style.transform = `translateY(-${bgMovement}px)`;

                // Phase-based animations
                if (this.scrollProgress <= 0.25) {
                    // Phase 1: Data Intelligence
                    this.analyticsLayer.style.opacity = this.scrollProgress * 4;
                    this.socialParticles.style.opacity = 0;
                    this.growthArrows.style.opacity = 0;
                    this.roiDisplay.style.opacity = 0;
                } else if (this.scrollProgress <= 0.5) {
                    // Phase 2: Social Media
                    const phaseProgress = (this.scrollProgress - 0.25) * 4;
                    this.analyticsLayer.style.opacity = 1 - phaseProgress * 0.5;
                    this.socialParticles.style.opacity = phaseProgress;
                    this.growthArrows.style.opacity = 0;
                    this.roiDisplay.style.opacity = 0;
                } else if (this.scrollProgress <= 0.75) {
                    // Phase 3: Growth
                    const phaseProgress = (this.scrollProgress - 0.5) * 4;
                    this.analyticsLayer.style.opacity = 0.5;
                    this.socialParticles.style.opacity = 1 - phaseProgress * 0.3;
                    this.growthArrows.style.opacity = phaseProgress;
                    this.roiDisplay.style.opacity = 0;
                } else {
                    // Phase 4: ROI
                    const phaseProgress = (this.scrollProgress - 0.75) * 4;
                    this.analyticsLayer.style.opacity = 0.5 + phaseProgress * 0.5;
                    this.socialParticles.style.opacity = 0.7;
                    this.growthArrows.style.opacity = 1;
                    this.roiDisplay.style.opacity = phaseProgress;
                }
            }

            animate() {
                // Continuous animations can go here
                requestAnimationFrame(() => this.animate());
            }
        }

        // Initialize the marketing platform animation
        document.addEventListener('DOMContentLoaded', () => {
            new MarketingPlatformAnimation();
            console.log('🚀 MarketFlow Pro Animation System Activated! 📈');
        });
    </script>
</body>
</html>
