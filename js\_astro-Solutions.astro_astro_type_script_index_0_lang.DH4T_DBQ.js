import{g as d}from"./index.Brfk6Bdo.js";let r=document.querySelectorAll(".navigation-item"),i=document.querySelector("#solutions-content"),o=0,a=null,c=Array.from(i?.querySelectorAll(".content-item")||[]).map((e,t)=>(e?.clientHeight>o&&(o=e.clientHeight),{el:e,navigation:r[t],logos:e.querySelectorAll(".item-container"),descriptionLines:e.querySelectorAll("p div")}));const g=()=>{r=document.querySelectorAll(".navigation-item"),i=document.querySelector("#solutions-content"),o=0,a=null,c=Array.from(i?.querySelectorAll(".content-item")||[]).map((e,t)=>(e?.clientHeight>o&&(o=e.clientHeight),{el:e,navigation:r[t],logos:e.querySelectorAll(".item-container"),descriptionLines:e.querySelectorAll("p div")})),l(),s(0),r.forEach((e,t)=>{e.addEventListener("click",()=>s(t))}),window.addEventListener("resize",l)},l=()=>{o=0,c.forEach(e=>{e.el.clientHeight>o&&(o=e.el.clientHeight)}),i&&d.set(i,{height:o})},s=e=>{if(a===e)return;const t=d.timeline();a=e,c.forEach((n,u)=>{u===e?(n.navigation.classList.add("active"),t.to(n.el,{pointerEvents:"auto",alpha:1,duration:.3,ease:"linear"},.3),n.descriptionLines=n.el.querySelectorAll("p div"),t.fromTo(n.descriptionLines,{y:"0.4em"},{y:0,duration:1.2,ease:"power2.out",stagger:{each:.15}},.3),t.fromTo(n.descriptionLines,{opacity:0},{opacity:1,duration:.8,stagger:{each:.15}},"<"),t.fromTo(n.logos,{opacity:0},{stagger:.1,opacity:1,duration:.3,ease:"linear"},"<+0.3")):(n.navigation.classList.remove("active"),t.to(n.el,{alpha:0,duration:.3,ease:"linear",pointerEvents:"none"},0))})};document.addEventListener("astro:page-load",g);document.addEventListener("astro:before-preparation",()=>{window.removeEventListener("resize",l)});
