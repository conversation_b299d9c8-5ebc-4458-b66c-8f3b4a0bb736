<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Montfort Group - Clean Version</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Century Gothic', '<PERSON><PERSON>', Arial, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Header and Navigation */
        header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 20px 0;
            border-bottom: 1px solid rgba(45, 98, 140, 0.1);
        }

        nav {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2d628c;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            color: #2d628c;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #4a7ba7;
        }

        /* News and Menu Buttons */
        .nav-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .news-btn, .menu-btn {
            background: transparent;
            border: 1px solid #2d628c;
            color: #2d628c;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .news-btn:hover, .menu-btn:hover {
            background: #2d628c;
            color: white;
        }

        .menu-btn {
            position: relative;
            width: 40px;
            height: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        /* Hamburger Icon */
        .hamburger {
            width: 20px;
            height: 2px;
            background: #2d628c;
            position: relative;
            transition: all 0.3s ease;
        }

        .hamburger::before,
        .hamburger::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 2px;
            background: #2d628c;
            transition: all 0.3s ease;
        }

        .hamburger::before {
            top: -6px;
        }

        .hamburger::after {
            top: 6px;
        }

        .menu-btn:hover .hamburger,
        .menu-btn:hover .hamburger::before,
        .menu-btn:hover .hamburger::after {
            background: white;
        }

        /* Menu active state */
        .menu-btn.active .hamburger {
            background: transparent;
        }

        .menu-btn.active .hamburger::before {
            transform: rotate(45deg);
            top: 0;
        }

        .menu-btn.active .hamburger::after {
            transform: rotate(-45deg);
            top: 0;
        }

        /* Menu Overlay - Original Mont-Fort Style */
        .menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f5f5f5;
            z-index: 2000;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            padding: 80px 60px 60px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s ease;
        }

        .menu-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .menu-content {
            width: 100%;
            max-width: 600px;
        }

        .menu-nav {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .menu-nav li {
            margin: 0;
            border-bottom: 1px solid rgba(45, 98, 140, 0.1);
        }

        .menu-nav li:last-child {
            border-bottom: none;
        }

        .menu-nav a {
            color: #2d628c;
            text-decoration: none;
            font-size: 2.5rem;
            font-weight: 300;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            display: block;
            padding: 25px 0;
            transition: all 0.3s ease;
            position: relative;
        }

        .menu-nav a:hover {
            color: #4a7ba7;
            padding-left: 20px;
        }

        /* Menu Footer */
        .menu-footer {
            position: absolute;
            bottom: 60px;
            left: 60px;
            right: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .menu-footer-links {
            display: flex;
            gap: 30px;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .menu-footer-links a {
            color: #2d628c;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 400;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            transition: color 0.3s ease;
        }

        .menu-footer-links a:hover {
            color: #4a7ba7;
        }

        /* Close Button - Top Right */
        .menu-close {
            position: absolute;
            top: 40px;
            right: 60px;
            background: transparent;
            border: none;
            color: #2d628c;
            font-size: 1.5rem;
            cursor: pointer;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-weight: 300;
        }

        .menu-close:hover {
            color: #4a7ba7;
            transform: rotate(90deg);
        }

        /* News Badge in Top Right */
        .menu-news {
            position: absolute;
            top: 40px;
            right: 120px;
            background: #2d628c;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* WebGL Background Container */
        #canvas-wrapper {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        canvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        /* Main Content */
        main {
            margin-top: 80px;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
        }

        .hero-content {
            max-width: 800px;
            padding: 0 20px;
        }

        .hero h1 {
            font-size: 4rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 0.2em;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .cta-button {
            display: inline-block;
            padding: 15px 30px;
            background: transparent;
            border: 2px solid white;
            color: white;
            text-decoration: none;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            background: white;
            color: #2d628c;
        }

        /* Content Sections */
        .section {
            padding: 100px 0;
            max-width: 1200px;
            margin: 0 auto;
            padding-left: 20px;
            padding-right: 20px;
        }

        .section h2 {
            font-size: 2.5rem;
            color: #2d628c;
            margin-bottom: 30px;
            text-align: center;
        }

        .section p {
            font-size: 1.1rem;
            text-align: center;
            max-width: 800px;
            margin: 0 auto 40px;
        }

        /* Grid Layout */
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }

        .card {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(45, 98, 140, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h3 {
            color: #2d628c;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        /* Footer */
        footer {
            background: #2d628c;
            color: white;
            padding: 60px 0;
            text-align: center;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .offices {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }

        .office h4 {
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .office p {
            opacity: 0.9;
            line-height: 1.8;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .news-btn {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .section {
                padding: 60px 0;
            }

            /* Mobile Menu Adjustments */
            .menu-overlay {
                padding: 60px 30px 40px;
            }

            .menu-nav a {
                font-size: 2rem;
                padding: 20px 0;
            }

            .menu-footer {
                bottom: 40px;
                left: 30px;
                right: 30px;
                flex-direction: column;
                gap: 20px;
                align-items: flex-start;
            }

            .menu-footer-links {
                flex-direction: column;
                gap: 15px;
            }

            .menu-close {
                top: 30px;
                right: 30px;
            }

            .menu-news {
                top: 30px;
                right: 80px;
            }
        }
    </style>
</head>
<body>
    <!-- WebGL Background -->
    <div id="canvas-wrapper">
        <canvas></canvas>
    </div>

    <!-- Header -->
    <header>
        <nav>
            <a href="#" class="logo">MONTFORT</a>

            <ul class="nav-links">
                <li><a href="#home">Montfort Group</a></li>
                <li><a href="#trading">Montfort Trading</a></li>
                <li><a href="#capital">Montfort Capital</a></li>
                <li><a href="#maritime">Montfort Maritime</a></li>
                <li><a href="#energy">Fort Energy</a></li>
            </ul>

            <div class="nav-actions">
                <a href="#news" class="news-btn">News</a>
                <button class="menu-btn" id="menuToggle">
                    <span class="hamburger"></span>
                </button>
            </div>
        </nav>
    </header>

    <!-- Menu Overlay - Original Mont-Fort Style -->
    <div class="menu-overlay" id="menuOverlay">
        <!-- News Badge -->
        <div class="menu-news">NEWS 01</div>

        <!-- Close Button -->
        <button class="menu-close" id="menuClose">+</button>

        <!-- Main Menu Content -->
        <div class="menu-content">
            <ul class="menu-nav">
                <li><a href="#home">MONTFORT GROUP</a></li>
                <li><a href="#trading">MONTFORT TRADING</a></li>
                <li><a href="#capital">MONTFORT CAPITAL</a></li>
                <li><a href="#maritime">MONTFORT MARITIME</a></li>
                <li><a href="#energy">FORT ENERGY</a></li>
            </ul>
        </div>

        <!-- Footer Links -->
        <div class="menu-footer">
            <ul class="menu-footer-links">
                <li><a href="#contact">CONTACT</a></li>
                <li><a href="#esg">ESG</a></li>
                <li><a href="#privacy">PRIVACY POLICY</a></li>
                <li><a href="#terms">TERMS OF USE</a></li>
            </ul>
            <div class="menu-footer-close">+</div>
        </div>
    </div>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="hero" data-chapter="Hero">
            <div class="hero-content">
                <h1>Montfort Group</h1>
                <p>A global commodity trading and asset investment company</p>
                <a href="#about" class="cta-button">Discover More</a>
            </div>
        </section>

        <!-- About Section -->
        <section class="section" data-chapter="WhoWeAre">
            <h2>Who We Are</h2>
            <p>Montfort is a global commodity trading and asset investment company. We trade, refine, store, and transport energy and commodities. We also invest in related assets and provide innovative services with integrity and efficiency to create long-term value for our clients.</p>
        </section>

        <!-- Services Section -->
        <section class="section" data-chapter="WhatWeDo">
            <h2>What We Do</h2>
            <p>We provide energy solutions with integrity and efficiency through our different business divisions.</p>
            
            <div class="grid">
                <div class="card">
                    <h3>Montfort Trading</h3>
                    <p>Operating Efficiently by Leading with Innovation.</p>
                </div>
                <div class="card">
                    <h3>Montfort Capital</h3>
                    <p>Identify and seize opportunities that maximise Value</p>
                </div>
                <div class="card">
                    <h3>Montfort Maritime</h3>
                    <p>Powering Progress, Delivering Energy.</p>
                </div>
                <div class="card">
                    <h3>Fort Energy</h3>
                    <p>Advancing Innovation in Energy Investments</p>
                </div>
            </div>
        </section>

        <!-- Global Section -->
        <section class="section" data-chapter="GlobalConnectivity">
            <h2>Global Connectivity</h2>
            <p>Established in the world's major trade hubs and financial markets with over 15 global offices, we connect and serve both emerging and mature markets worldwide.</p>
        </section>

        <!-- Sustainability Section -->
        <section class="section" data-chapter="Sustainability">
            <h2>Sustainability</h2>
            <p>We are committed to integrating our sustainability strategy with our pursuit of value — powering lives and respecting nature. We recognize the profound and lasting impact our decisions have on people, communities, and the environment.</p>
        </section>
    </main>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <h2>Contact Us</h2>
            <div class="offices">
                <div class="office">
                    <h4>Geneva, Switzerland</h4>
                    <p>3rd & 4th floor Rue du Mont-Blanc 14<br>1201 Geneva, Switzerland<br>P: +41 227415900</p>
                </div>
                <div class="office">
                    <h4>Dubai, UAE</h4>
                    <p>1104 ICD Brookfield Place<br>Dubai International Financial Centre<br>P: +971 45914032</p>
                </div>
                <div class="office">
                    <h4>Singapore</h4>
                    <p>0804 Marina One East Tower<br>7 Straits View<br>018936, Singapore<br>P: +65 62286490</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- WebGL Script -->
    <script type="module">
        // Import the WebGL app
        import { a as webglApp } from './js/GlobalApp.CGTlQdR2.js';
        
        const wrapper = document.querySelector('#canvas-wrapper');
        const canvas = wrapper.querySelector('canvas');
        
        console.log('Initializing clean Montfort background...');
        
        try {
            await webglApp.init(wrapper, canvas);
            console.log('WebGL background initialized successfully');
        } catch (error) {
            console.error('Failed to initialize WebGL background:', error);
            // Fallback gradient background
            wrapper.style.background = 'linear-gradient(135deg, #2d628c 0%, #4a7ba7 50%, #8cb4d5 100%)';
        }
    </script>

    <!-- Menu Toggle Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const menuToggle = document.getElementById('menuToggle');
            const menuOverlay = document.getElementById('menuOverlay');
            const menuClose = document.getElementById('menuClose');

            // Open menu
            menuToggle.addEventListener('click', function() {
                menuToggle.classList.add('active');
                menuOverlay.classList.add('active');
                document.body.style.overflow = 'hidden'; // Prevent scrolling
            });

            // Close menu
            function closeMenu() {
                menuToggle.classList.remove('active');
                menuOverlay.classList.remove('active');
                document.body.style.overflow = ''; // Restore scrolling
            }

            menuClose.addEventListener('click', closeMenu);

            // Close menu when clicking on overlay background
            menuOverlay.addEventListener('click', function(e) {
                if (e.target === menuOverlay) {
                    closeMenu();
                }
            });

            // Close menu when pressing Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && menuOverlay.classList.contains('active')) {
                    closeMenu();
                }
            });

            // Close menu when clicking on menu links
            const menuLinks = document.querySelectorAll('.menu-nav a');
            menuLinks.forEach(link => {
                link.addEventListener('click', closeMenu);
            });

            console.log('Menu functionality initialized');
        });
    </script>
</body>
</html>
