<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Montfort Group - Clean Version</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Century Gothic', '<PERSON><PERSON>', Arial, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Header and Navigation - Exact Original Style */
        header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 20px 0;
        }

        nav {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 40px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            font-size: 1rem;
            font-weight: 400;
            color: #2d628c;
            text-decoration: none;
            text-transform: uppercase;
            letter-spacing: 0.1em;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 50px;
            margin: 0;
            padding: 0;
        }

        .nav-links a {
            color: #2d628c;
            text-decoration: none;
            font-weight: 400;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            transition: color 0.3s ease;
            opacity: 0.7;
        }

        .nav-links a:hover {
            opacity: 1;
        }

        /* News and Menu Buttons - Exact Original Style */
        .nav-actions {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .news-btn {
            background: #2d628c;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 400;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
        }

        .news-badge {
            background: white;
            color: #2d628c;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.65rem;
            font-weight: 600;
        }

        .news-btn:hover {
            background: #4a7ba7;
        }

        .menu-btn {
            background: transparent;
            border: none;
            color: #2d628c;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 400;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            transition: all 0.3s ease;
            padding: 0;
            opacity: 0.7;
        }

        .menu-btn:hover {
            opacity: 1;
        }

        /* Menu Overlay - Original Mont-Fort Style */
        .menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f5f5f5;
            z-index: 2000;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            padding: 80px 60px 60px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s ease;
        }

        .menu-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .menu-content {
            width: 100%;
            max-width: 600px;
        }

        .menu-nav {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .menu-nav li {
            margin: 0;
            border-bottom: 1px solid rgba(45, 98, 140, 0.1);
        }

        .menu-nav li:last-child {
            border-bottom: none;
        }

        .menu-nav a {
            color: #2d628c;
            text-decoration: none;
            font-size: 2.5rem;
            font-weight: 300;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            display: block;
            padding: 25px 0;
            transition: all 0.3s ease;
            position: relative;
        }

        .menu-nav a:hover {
            color: #4a7ba7;
            padding-left: 20px;
        }

        /* Menu Footer */
        .menu-footer {
            position: absolute;
            bottom: 60px;
            left: 60px;
            right: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .menu-footer-links {
            display: flex;
            gap: 30px;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .menu-footer-links a {
            color: #2d628c;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 400;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            transition: color 0.3s ease;
        }

        .menu-footer-links a:hover {
            color: #4a7ba7;
        }

        /* Close Button - Top Right */
        .menu-close {
            position: absolute;
            top: 40px;
            right: 60px;
            background: transparent;
            border: none;
            color: #2d628c;
            font-size: 1.5rem;
            cursor: pointer;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-weight: 300;
        }

        .menu-close:hover {
            color: #4a7ba7;
            transform: rotate(90deg);
        }

        /* News Badge in Top Right */
        .menu-news {
            position: absolute;
            top: 40px;
            right: 120px;
            background: #2d628c;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* WebGL Background Container */
        #canvas-wrapper {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        canvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        /* Main Content */
        main {
            margin-top: 80px;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Hero Section with Parallax Background */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero-bg {
            position: absolute;
            top: -10%;
            left: -10%;
            width: 120%;
            height: 120%;
            background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            transition: transform 0.1s ease-out;
            z-index: -2;
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(45, 98, 140, 0.4);
            z-index: -1;
        }

        .hero-content {
            max-width: 800px;
            padding: 0 20px;
            z-index: 1;
        }

        .hero h1 {
            font-size: 4rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 0.2em;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .cta-button {
            display: inline-block;
            padding: 15px 30px;
            background: transparent;
            border: 2px solid white;
            color: white;
            text-decoration: none;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            background: white;
            color: #2d628c;
        }

        /* Full-Screen Scrollable Background Sections */
        .section {
            height: 100vh;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .section-content {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 40px;
            position: relative;
            z-index: 3;
            text-align: center;
            color: white;
        }

        .section-bg {
            position: absolute;
            top: -20%;
            left: -20%;
            width: 140%;
            height: 140%;
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            transition: transform 0.1s ease-out;
            z-index: 1;
        }

        .section-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(45, 98, 140, 0.6);
            z-index: 2;
        }

        .section h2 {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 300;
            text-transform: uppercase;
            letter-spacing: 0.1em;
        }

        .section p {
            font-size: 1.3rem;
            text-align: center;
            max-width: 800px;
            margin: 0 auto 40px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.8;
        }

        /* Specific section backgrounds with different themes */
        .who-we-are .section-bg {
            background-image: url('https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
        }

        .who-we-are .section-overlay {
            background: rgba(45, 98, 140, 0.7);
        }

        .what-we-do .section-bg {
            background-image: url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
        }

        .what-we-do .section-overlay {
            background: rgba(30, 60, 90, 0.8);
        }

        .global-connectivity .section-bg {
            background-image: url('https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2072&q=80');
        }

        .global-connectivity .section-overlay {
            background: rgba(20, 40, 80, 0.7);
        }

        .sustainability .section-bg {
            background-image: url('https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
        }

        .sustainability .section-overlay {
            background: rgba(40, 80, 60, 0.7);
        }

        /* Grid Layout */
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }

        /* Service Grid for What We Do section */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 40px;
            margin-top: 60px;
            max-width: 800px;
        }

        .service-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .service-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-5px);
        }

        .service-item h3 {
            color: white;
            margin-bottom: 15px;
            font-size: 1.3rem;
            font-weight: 500;
        }

        .service-item p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1rem;
            margin: 0;
        }

        /* Footer */
        footer {
            background: #2d628c;
            color: white;
            padding: 60px 0;
            text-align: center;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .offices {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }

        .office h4 {
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .office p {
            opacity: 0.9;
            line-height: 1.8;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .nav-links {
                display: none;
            }
        }

        @media (max-width: 768px) {
            nav {
                padding: 0 20px;
            }

            .news-btn {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .section {
                padding: 60px 0;
            }

            /* Mobile Menu Adjustments */
            .menu-overlay {
                padding: 60px 30px 40px;
            }

            .menu-nav a {
                font-size: 2rem;
                padding: 20px 0;
            }

            .menu-footer {
                bottom: 40px;
                left: 30px;
                right: 30px;
                flex-direction: column;
                gap: 20px;
                align-items: flex-start;
            }

            .menu-footer-links {
                flex-direction: column;
                gap: 15px;
            }

            .menu-close {
                top: 30px;
                right: 30px;
            }

            .menu-news {
                top: 30px;
                right: 80px;
            }
        }
    </style>
</head>
<body>
    <!-- WebGL Background -->
    <div id="canvas-wrapper">
        <canvas></canvas>
    </div>

    <!-- Header - Original Mont-Fort Style -->
    <header>
        <nav>
            <a href="#" class="logo">MONTFORT GROUP</a>

            <ul class="nav-links">
                <li><a href="#trading">Montfort Trading</a></li>
                <li><a href="#capital">Montfort Capital</a></li>
                <li><a href="#maritime">Montfort Maritime</a></li>
                <li><a href="#energy">Fort Energy</a></li>
            </ul>

            <div class="nav-actions">
                <a href="#news" class="news-btn">
                    NEWS
                    <span class="news-badge">01</span>
                </a>
                <button class="menu-btn" id="menuToggle">MENU</button>
            </div>
        </nav>
    </header>

    <!-- Menu Overlay - Original Mont-Fort Style -->
    <div class="menu-overlay" id="menuOverlay">
        <!-- News Badge -->
        <div class="menu-news">NEWS 01</div>

        <!-- Close Button -->
        <button class="menu-close" id="menuClose">+</button>

        <!-- Main Menu Content -->
        <div class="menu-content">
            <ul class="menu-nav">
                <li><a href="#home">MONTFORT GROUP</a></li>
                <li><a href="#trading">MONTFORT TRADING</a></li>
                <li><a href="#capital">MONTFORT CAPITAL</a></li>
                <li><a href="#maritime">MONTFORT MARITIME</a></li>
                <li><a href="#energy">FORT ENERGY</a></li>
            </ul>
        </div>

        <!-- Footer Links -->
        <div class="menu-footer">
            <ul class="menu-footer-links">
                <li><a href="#contact">CONTACT</a></li>
                <li><a href="#esg">ESG</a></li>
                <li><a href="#privacy">PRIVACY POLICY</a></li>
                <li><a href="#terms">TERMS OF USE</a></li>
            </ul>
            <div class="menu-footer-close">+</div>
        </div>
    </div>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="hero" data-chapter="Hero">
            <div class="hero-bg"></div>
            <div class="hero-overlay"></div>
            <div class="hero-content">
                <h1>Montfort Group</h1>
                <p>A global commodity trading and asset investment company</p>
                <a href="#about" class="cta-button">Discover More</a>
            </div>
        </section>

        <!-- About Section -->
        <section class="section who-we-are" data-chapter="WhoWeAre">
            <div class="section-bg"></div>
            <div class="section-overlay"></div>
            <div class="section-content">
                <h2>Who We Are</h2>
                <p>Montfort is a global commodity trading and asset investment company. We trade, refine, store, and transport energy and commodities. We also invest in related assets and provide innovative services with integrity and efficiency to create long-term value for our clients.</p>
            </div>
        </section>

        <!-- Services Section -->
        <section class="section what-we-do" data-chapter="WhatWeDo">
            <div class="section-bg"></div>
            <div class="section-overlay"></div>
            <div class="section-content">
                <h2>What We Do</h2>
                <p>We provide energy solutions with integrity and efficiency through our different business divisions.</p>

                <div class="services-grid">
                    <div class="service-item">
                        <h3>Montfort Trading</h3>
                        <p>Operating Efficiently by Leading with Innovation.</p>
                    </div>
                    <div class="service-item">
                        <h3>Montfort Capital</h3>
                        <p>Identify and seize opportunities that maximise Value</p>
                    </div>
                    <div class="service-item">
                        <h3>Montfort Maritime</h3>
                        <p>Powering Progress, Delivering Energy.</p>
                    </div>
                    <div class="service-item">
                        <h3>Fort Energy</h3>
                        <p>Advancing Innovation in Energy Investments</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Global Section -->
        <section class="section global-connectivity" data-chapter="GlobalConnectivity">
            <div class="section-bg"></div>
            <div class="section-overlay"></div>
            <div class="section-content">
                <h2>Global Connectivity</h2>
                <p>Established in the world's major trade hubs and financial markets with over 15 global offices, we connect and serve both emerging and mature markets worldwide.</p>
            </div>
        </section>

        <!-- Sustainability Section -->
        <section class="section sustainability" data-chapter="Sustainability">
            <div class="section-bg"></div>
            <div class="section-overlay"></div>
            <div class="section-content">
                <h2>Sustainability</h2>
                <p>We are committed to integrating our sustainability strategy with our pursuit of value — powering lives and respecting nature. We recognize the profound and lasting impact our decisions have on people, communities, and the environment.</p>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <h2>Contact Us</h2>
            <div class="offices">
                <div class="office">
                    <h4>Geneva, Switzerland</h4>
                    <p>3rd & 4th floor Rue du Mont-Blanc 14<br>1201 Geneva, Switzerland<br>P: +41 227415900</p>
                </div>
                <div class="office">
                    <h4>Dubai, UAE</h4>
                    <p>1104 ICD Brookfield Place<br>Dubai International Financial Centre<br>P: +971 45914032</p>
                </div>
                <div class="office">
                    <h4>Singapore</h4>
                    <p>0804 Marina One East Tower<br>7 Straits View<br>018936, Singapore<br>P: +65 62286490</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- WebGL Script -->
    <script type="module">
        // Import the WebGL app
        import { a as webglApp } from './js/GlobalApp.CGTlQdR2.js';
        
        const wrapper = document.querySelector('#canvas-wrapper');
        const canvas = wrapper.querySelector('canvas');
        
        console.log('Initializing clean Montfort background...');
        
        try {
            await webglApp.init(wrapper, canvas);
            console.log('WebGL background initialized successfully');
        } catch (error) {
            console.error('Failed to initialize WebGL background:', error);
            // Fallback gradient background
            wrapper.style.background = 'linear-gradient(135deg, #2d628c 0%, #4a7ba7 50%, #8cb4d5 100%)';
        }
    </script>

    <!-- Menu Toggle Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const menuToggle = document.getElementById('menuToggle');
            const menuOverlay = document.getElementById('menuOverlay');
            const menuClose = document.getElementById('menuClose');

            // Open menu
            menuToggle.addEventListener('click', function() {
                menuToggle.classList.add('active');
                menuOverlay.classList.add('active');
                document.body.style.overflow = 'hidden'; // Prevent scrolling
            });

            // Close menu
            function closeMenu() {
                menuToggle.classList.remove('active');
                menuOverlay.classList.remove('active');
                document.body.style.overflow = ''; // Restore scrolling
            }

            menuClose.addEventListener('click', closeMenu);

            // Close menu when clicking on overlay background
            menuOverlay.addEventListener('click', function(e) {
                if (e.target === menuOverlay) {
                    closeMenu();
                }
            });

            // Close menu when pressing Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && menuOverlay.classList.contains('active')) {
                    closeMenu();
                }
            });

            // Close menu when clicking on menu links
            const menuLinks = document.querySelectorAll('.menu-nav a');
            menuLinks.forEach(link => {
                link.addEventListener('click', closeMenu);
            });

            console.log('Menu functionality initialized');
        });
    </script>

    <!-- Enhanced Parallax and Zoom Effects Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Enhanced parallax and zoom effects for full-screen sections
            function updateScrollEffects() {
                const scrollY = window.scrollY;
                const windowHeight = window.innerHeight;

                // Hero section effects
                const hero = document.querySelector('.hero');
                const heroBg = document.querySelector('.hero-bg');
                if (hero && heroBg) {
                    const heroRect = hero.getBoundingClientRect();
                    const heroProgress = Math.max(0, Math.min(1, -heroRect.top / windowHeight));

                    // Dramatic zoom out effect as you scroll away from hero
                    const zoomScale = 1 + (heroProgress * 0.5);
                    const parallaxY = heroProgress * 100;
                    heroBg.style.transform = `scale(${zoomScale}) translateY(${parallaxY}px)`;
                }

                // All sections with different dramatic effects
                const sections = document.querySelectorAll('.section');
                sections.forEach((section, index) => {
                    const sectionBg = section.querySelector('.section-bg');
                    if (sectionBg) {
                        const rect = section.getBoundingClientRect();
                        const isVisible = rect.top < windowHeight && rect.bottom > 0;

                        if (isVisible) {
                            // Calculate progress through the section
                            const sectionProgress = Math.max(0, Math.min(1, (windowHeight - rect.top) / (windowHeight + rect.height)));
                            const centerProgress = Math.abs(0.5 - sectionProgress) * 2; // 0 at center, 1 at edges

                            // Different dramatic effects for each section
                            switch(index) {
                                case 0: // Who We Are - Zoom in effect
                                    const zoomIn = 1 + (sectionProgress * 0.4);
                                    const rotateIn = (sectionProgress - 0.5) * 2;
                                    sectionBg.style.transform = `scale(${zoomIn}) rotate(${rotateIn}deg)`;
                                    break;

                                case 1: // What We Do - Parallax with zoom
                                    const parallaxY = (sectionProgress - 0.5) * 200;
                                    const zoomParallax = 1 + (centerProgress * 0.3);
                                    sectionBg.style.transform = `translateY(${parallaxY}px) scale(${zoomParallax})`;
                                    break;

                                case 2: // Global Connectivity - 3D rotation effect
                                    const rotateX = (sectionProgress - 0.5) * 10;
                                    const rotateY = (sectionProgress - 0.5) * 5;
                                    const zoomRotate = 1 + (sectionProgress * 0.2);
                                    sectionBg.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(${zoomRotate})`;
                                    break;

                                case 3: // Sustainability - Organic zoom and movement
                                    const organicZoom = 1 + (Math.sin(sectionProgress * Math.PI) * 0.3);
                                    const organicX = Math.sin(sectionProgress * Math.PI * 2) * 50;
                                    const organicY = Math.cos(sectionProgress * Math.PI * 2) * 30;
                                    sectionBg.style.transform = `scale(${organicZoom}) translate(${organicX}px, ${organicY}px)`;
                                    break;
                            }
                        }
                    }
                });
            }

            // Enhanced mouse parallax effect for all sections
            function updateMouseParallax(e) {
                const mouseX = (e.clientX / window.innerWidth - 0.5);
                const mouseY = (e.clientY / window.innerHeight - 0.5);

                // Apply mouse parallax to hero
                const heroBg = document.querySelector('.hero-bg');
                if (heroBg) {
                    const currentTransform = heroBg.style.transform || '';
                    const intensity = 30;
                    const newMouseX = mouseX * intensity;
                    const newMouseY = mouseY * intensity;

                    // Preserve existing transforms and add mouse movement
                    if (currentTransform.includes('scale') || currentTransform.includes('translateY')) {
                        const baseTransform = currentTransform.replace(/translate\([^)]*\)/g, '');
                        heroBg.style.transform = `${baseTransform} translate(${newMouseX}px, ${newMouseY}px)`;
                    } else {
                        heroBg.style.transform = `translate(${newMouseX}px, ${newMouseY}px)`;
                    }
                }

                // Apply subtle mouse parallax to section backgrounds
                const sectionBgs = document.querySelectorAll('.section-bg');
                sectionBgs.forEach((bg, index) => {
                    const intensity = 15 - (index * 3); // Decreasing intensity for each section
                    const newMouseX = mouseX * intensity;
                    const newMouseY = mouseY * intensity;

                    const currentTransform = bg.style.transform || '';
                    if (currentTransform && !currentTransform.includes('translate(')) {
                        bg.style.transform = `${currentTransform} translate(${newMouseX}px, ${newMouseY}px)`;
                    }
                });
            }

            // Smooth scroll behavior for better effect visibility
            function smoothScrollTo(targetY, duration = 1000) {
                const startY = window.scrollY;
                const distance = targetY - startY;
                const startTime = performance.now();

                function step(currentTime) {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);
                    const ease = 0.5 - 0.5 * Math.cos(progress * Math.PI); // Smooth easing

                    window.scrollTo(0, startY + distance * ease);

                    if (progress < 1) {
                        requestAnimationFrame(step);
                    }
                }

                requestAnimationFrame(step);
            }

            // Add click handlers for smooth scrolling to sections
            const navLinks = document.querySelectorAll('.nav-links a, .menu-nav a');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    const href = this.getAttribute('href');
                    if (href.startsWith('#')) {
                        e.preventDefault();
                        const target = document.querySelector(href);
                        if (target) {
                            smoothScrollTo(target.offsetTop);
                        }
                    }
                });
            });

            // Event listeners
            window.addEventListener('scroll', updateScrollEffects);
            window.addEventListener('mousemove', updateMouseParallax);

            // Initial call
            updateScrollEffects();

            console.log('Enhanced parallax and zoom effects initialized');
        });
    </script>
</body>
</html>
