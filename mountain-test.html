<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mountain Background Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #000;
            color: white;
            overflow: hidden;
        }
        
        #canvas-wrapper {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        
        canvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .overlay {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 10;
            background: rgba(0, 0, 0, 0.7);
            padding: 20px;
            border-radius: 10px;
        }
        
        h1 {
            font-size: 3rem;
            margin: 0 0 20px 0;
            color: #2d628c;
        }
        
        p {
            font-size: 1.2rem;
            margin: 10px 0;
        }
        
        .status {
            color: #4a7ba7;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="canvas-wrapper">
        <canvas></canvas>
    </div>
    
    <div class="overlay">
        <h1>MONTFORT</h1>
        <p>Mountain Background Test</p>
        <p class="status" id="status">Loading mountain texture...</p>
    </div>

    <script type="module">
        // Import the WebGL app
        import { a as webglApp } from './js/GlobalApp.CGTlQdR2.js';
        
        const wrapper = document.querySelector('#canvas-wrapper');
        const canvas = wrapper.querySelector('canvas');
        const status = document.getElementById('status');
        
        console.log('Initializing mountain background test...');
        status.textContent = 'Initializing WebGL...';
        
        try {
            await webglApp.init(wrapper, canvas);
            status.textContent = 'Mountain texture loaded successfully!';
            console.log('Mountain background test initialized successfully');
        } catch (error) {
            console.error('Failed to initialize mountain background:', error);
            status.textContent = 'Failed to load mountain texture';
            status.style.color = '#ff6b6b';
        }
    </script>
</body>
</html>
