// ScrollTrigger functionality
export default class ScrollTrigger {
    constructor() {
        this.init();
    }
    
    init() {
        // Basic scroll trigger functionality
        window.addEventListener('scroll', this.handleScroll.bind(this));
        console.log('ScrollTrigger initialized');
    }
    
    handleScroll() {
        // Handle scroll events
        const scrollY = window.scrollY;
        const elements = document.querySelectorAll('[data-scroll]');
        
        elements.forEach(element => {
            const rect = element.getBoundingClientRect();
            if (rect.top < window.innerHeight && rect.bottom > 0) {
                element.classList.add('in-view');
            }
        });
    }
}
