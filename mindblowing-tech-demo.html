<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mind-Blowing Technology Animation</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', monospace;
            background: #000;
            overflow-x: hidden;
            cursor: none;
        }

        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

        /* Custom Cursor */
        .custom-cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, #00ffff, transparent);
            border-radius: 50%;
            pointer-events: none;
            z-index: 10000;
            mix-blend-mode: difference;
            transition: transform 0.1s ease;
        }

        .cursor-trail {
            position: fixed;
            width: 4px;
            height: 4px;
            background: #00ffff;
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            opacity: 0;
        }

        /* WebGL Canvas */
        #webgl-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: -1;
        }

        /* Particle System Overlay */
        .particle-system {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none;
        }

        .quantum-particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00ffff;
            border-radius: 50%;
            box-shadow: 0 0 10px #00ffff;
        }

        /* Holographic UI Elements */
        .holo-interface {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2;
            pointer-events: none;
        }

        .holo-circle {
            position: absolute;
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 50%;
            animation: holo-rotate 20s linear infinite;
        }

        .holo-circle:nth-child(1) {
            width: 200px;
            height: 200px;
            top: 10%;
            left: 10%;
            animation-duration: 15s;
        }

        .holo-circle:nth-child(2) {
            width: 300px;
            height: 300px;
            top: 60%;
            right: 10%;
            animation-duration: 25s;
            animation-direction: reverse;
        }

        .holo-circle:nth-child(3) {
            width: 150px;
            height: 150px;
            bottom: 20%;
            left: 50%;
            animation-duration: 18s;
        }

        @keyframes holo-rotate {
            0% { transform: rotate(0deg) scale(1); opacity: 0.3; }
            50% { transform: rotate(180deg) scale(1.2); opacity: 0.7; }
            100% { transform: rotate(360deg) scale(1); opacity: 0.3; }
        }

        /* Neural Network Visualization */
        .neural-network {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 3;
            pointer-events: none;
        }

        .neural-node {
            position: absolute;
            width: 8px;
            height: 8px;
            background: radial-gradient(circle, #ff0080, transparent);
            border-radius: 50%;
            animation: neural-pulse 2s ease-in-out infinite;
        }

        .neural-connection {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, transparent, #ff0080, transparent);
            transform-origin: left center;
            animation: neural-flow 3s ease-in-out infinite;
        }

        @keyframes neural-pulse {
            0%, 100% { transform: scale(1); opacity: 0.6; }
            50% { transform: scale(2); opacity: 1; box-shadow: 0 0 20px #ff0080; }
        }

        @keyframes neural-flow {
            0%, 100% { opacity: 0; }
            50% { opacity: 1; }
        }

        /* Matrix Rain Effect */
        .matrix-rain {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 4;
            pointer-events: none;
            opacity: 0.1;
        }

        .matrix-column {
            position: absolute;
            top: -100%;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #00ff00;
            animation: matrix-fall linear infinite;
            text-shadow: 0 0 5px #00ff00;
        }

        @keyframes matrix-fall {
            0% { top: -100%; opacity: 1; }
            100% { top: 100vh; opacity: 0; }
        }

        /* Content Sections */
        .content-section {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 100;
            padding: 4rem 2rem;
            text-align: center;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border: 2px solid transparent;
            background-clip: padding-box;
            margin: 2rem;
            border-radius: 20px;
            transition: all 0.5s ease;
        }

        .content-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 20px;
            padding: 2px;
            background: linear-gradient(45deg, #00ffff, #ff0080, #00ff00, #ffff00);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            animation: border-glow 3s ease-in-out infinite;
        }

        @keyframes border-glow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .content-section:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 255, 255, 0.3);
        }

        .content-section h1 {
            font-size: 4rem;
            font-weight: 900;
            background: linear-gradient(45deg, #00ffff, #ff0080, #00ff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2rem;
            animation: text-shimmer 2s ease-in-out infinite;
        }

        .content-section h2 {
            font-size: 3rem;
            font-weight: 700;
            color: #00ffff;
            margin-bottom: 1.5rem;
            text-shadow: 0 0 20px #00ffff;
            animation: text-glow 2s ease-in-out infinite alternate;
        }

        .content-section p {
            font-size: 1.3rem;
            line-height: 1.8;
            color: rgba(255, 255, 255, 0.9);
            max-width: 800px;
            margin-bottom: 2rem;
        }

        @keyframes text-shimmer {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes text-glow {
            0% { text-shadow: 0 0 20px #00ffff; }
            100% { text-shadow: 0 0 30px #00ffff, 0 0 40px #00ffff; }
        }

        .quantum-button {
            background: linear-gradient(45deg, transparent, rgba(0, 255, 255, 0.2), transparent);
            border: 2px solid #00ffff;
            color: #00ffff;
            padding: 1rem 2rem;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            text-transform: uppercase;
            border-radius: 10px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .quantum-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.4), transparent);
            transition: left 0.5s ease;
        }

        .quantum-button:hover::before {
            left: 100%;
        }

        .quantum-button:hover {
            transform: scale(1.05);
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
            text-shadow: 0 0 10px #00ffff;
        }

        /* Audio Visualizer */
        .audio-visualizer {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 60px;
            z-index: 1000;
            display: flex;
            align-items: end;
            gap: 2px;
        }

        .audio-bar {
            width: 4px;
            background: linear-gradient(to top, #ff0080, #00ffff);
            border-radius: 2px;
            animation: audio-pulse 0.5s ease-in-out infinite alternate;
        }

        @keyframes audio-pulse {
            0% { height: 10px; }
            100% { height: var(--height, 30px); }
        }

        /* Glitch Effects */
        .glitch-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 5;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .glitch-line {
            position: absolute;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #ff0080, #00ffff, #ff0080);
            animation: glitch-scan 2s linear infinite;
        }

        @keyframes glitch-scan {
            0% { top: 0; opacity: 1; }
            100% { top: 100vh; opacity: 0; }
        }
    </style>
</head>
<body>
    <!-- Custom Cursor -->
    <div class="custom-cursor" id="cursor"></div>

    <!-- WebGL 3D Background -->
    <canvas id="webgl-canvas"></canvas>

    <!-- Particle System -->
    <div class="particle-system" id="particleSystem"></div>

    <!-- Holographic Interface -->
    <div class="holo-interface">
        <div class="holo-circle"></div>
        <div class="holo-circle"></div>
        <div class="holo-circle"></div>
    </div>

    <!-- Neural Network -->
    <div class="neural-network" id="neuralNetwork"></div>

    <!-- Matrix Rain -->
    <div class="matrix-rain" id="matrixRain"></div>

    <!-- Glitch Overlay -->
    <div class="glitch-overlay" id="glitchOverlay">
        <div class="glitch-line"></div>
        <div class="glitch-line" style="animation-delay: 0.5s;"></div>
        <div class="glitch-line" style="animation-delay: 1s;"></div>
    </div>

    <!-- Audio Visualizer -->
    <div class="audio-visualizer" id="audioViz"></div>

    <!-- Content Sections -->
    <section class="content-section">
        <h1>QUANTUM REALITY</h1>
        <p>Experience the intersection of quantum mechanics and digital consciousness. Watch as reality bends and particles exist in multiple states simultaneously, defying the laws of classical physics.</p>
        <button class="quantum-button">Enter Quantum Realm</button>
    </section>

    <section class="content-section">
        <h2>NEURAL SINGULARITY</h2>
        <p>Witness the birth of artificial consciousness as neural networks achieve unprecedented complexity. Each connection represents a thought, each pulse a moment of digital awareness.</p>
        <button class="quantum-button">Merge Consciousness</button>
    </section>

    <section class="content-section">
        <h2>DIMENSIONAL MATRIX</h2>
        <p>Navigate through layers of reality where code becomes matter and algorithms shape existence. The boundary between digital and physical dissolves into pure information.</p>
        <button class="quantum-button">Hack Reality</button>
    </section>

    <section class="content-section">
        <h2>INFINITE POSSIBILITIES</h2>
        <p>Step beyond the event horizon of technological evolution where human imagination merges with unlimited computational power to create new universes of possibility.</p>
        <button class="quantum-button">Transcend Limits</button>
    </section>

    <script>
        class MindBlowingTechSystem {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.particles = [];
                this.neuralNodes = [];
                this.mouseX = 0;
                this.mouseY = 0;
                this.time = 0;

                this.init();
            }

            init() {
                this.setupWebGL();
                this.createParticleSystem();
                this.createNeuralNetwork();
                this.createMatrixRain();
                this.setupAudioVisualizer();
                this.setupInteractivity();
                this.animate();
            }

            setupWebGL() {
                // Create Three.js scene
                this.scene = new THREE.Scene();
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.renderer = new THREE.WebGLRenderer({
                    canvas: document.getElementById('webgl-canvas'),
                    alpha: true,
                    antialias: true
                });

                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setClearColor(0x000000, 0);

                // Create 3D particle system
                this.create3DParticles();

                // Create morphing geometry
                this.createMorphingGeometry();

                this.camera.position.z = 5;
            }

            create3DParticles() {
                const particleCount = 5000;
                const geometry = new THREE.BufferGeometry();
                const positions = new Float32Array(particleCount * 3);
                const colors = new Float32Array(particleCount * 3);
                const sizes = new Float32Array(particleCount);

                for (let i = 0; i < particleCount; i++) {
                    const i3 = i * 3;

                    // Random positions in 3D space
                    positions[i3] = (Math.random() - 0.5) * 20;
                    positions[i3 + 1] = (Math.random() - 0.5) * 20;
                    positions[i3 + 2] = (Math.random() - 0.5) * 20;

                    // Random colors
                    colors[i3] = Math.random();
                    colors[i3 + 1] = Math.random() * 0.5 + 0.5;
                    colors[i3 + 2] = 1;

                    sizes[i] = Math.random() * 3 + 1;
                }

                geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
                geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
                geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

                const material = new THREE.ShaderMaterial({
                    uniforms: {
                        time: { value: 0 },
                        mouse: { value: new THREE.Vector2() }
                    },
                    vertexShader: `
                        attribute float size;
                        attribute vec3 color;
                        varying vec3 vColor;
                        uniform float time;
                        uniform vec2 mouse;

                        void main() {
                            vColor = color;
                            vec3 pos = position;

                            // Wave motion
                            pos.y += sin(pos.x * 0.5 + time) * 0.5;
                            pos.x += cos(pos.z * 0.5 + time) * 0.3;

                            // Mouse interaction
                            vec2 mouseInfluence = mouse * 0.1;
                            pos.xy += mouseInfluence * (1.0 / (distance(pos.xy, mouseInfluence) + 1.0));

                            vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
                            gl_PointSize = size * (300.0 / -mvPosition.z);
                            gl_Position = projectionMatrix * mvPosition;
                        }
                    `,
                    fragmentShader: `
                        varying vec3 vColor;

                        void main() {
                            float distance = length(gl_PointCoord - vec2(0.5));
                            if (distance > 0.5) discard;

                            float alpha = 1.0 - distance * 2.0;
                            gl_FragColor = vec4(vColor, alpha);
                        }
                    `,
                    transparent: true,
                    vertexColors: true
                });

                this.particleSystem = new THREE.Points(geometry, material);
                this.scene.add(this.particleSystem);
            }

            createMorphingGeometry() {
                // Create morphing torus that responds to scroll
                const geometry = new THREE.TorusGeometry(2, 0.5, 16, 100);
                const material = new THREE.ShaderMaterial({
                    uniforms: {
                        time: { value: 0 },
                        scroll: { value: 0 }
                    },
                    vertexShader: `
                        uniform float time;
                        uniform float scroll;

                        void main() {
                            vec3 pos = position;

                            // Morphing effect
                            pos += normal * sin(pos.x * 2.0 + time) * 0.1;
                            pos += normal * cos(pos.y * 3.0 + time * 1.5) * 0.05;

                            // Scroll-based transformation
                            pos *= 1.0 + scroll * 0.5;

                            gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
                        }
                    `,
                    fragmentShader: `
                        uniform float time;

                        void main() {
                            vec3 color = vec3(
                                sin(time) * 0.5 + 0.5,
                                cos(time * 1.3) * 0.5 + 0.5,
                                sin(time * 0.7) * 0.5 + 0.5
                            );
                            gl_FragColor = vec4(color, 0.8);
                        }
                    `,
                    transparent: true,
                    wireframe: true
                });

                this.morphingTorus = new THREE.Mesh(geometry, material);
                this.scene.add(this.morphingTorus);
            }

            createParticleSystem() {
                // Quantum particle system with physics
                const container = document.getElementById('particleSystem');

                for (let i = 0; i < 200; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'quantum-particle';

                    const physics = {
                        x: Math.random() * window.innerWidth,
                        y: Math.random() * window.innerHeight,
                        vx: (Math.random() - 0.5) * 2,
                        vy: (Math.random() - 0.5) * 2,
                        life: Math.random() * 100 + 50
                    };

                    particle.physics = physics;
                    container.appendChild(particle);
                    this.particles.push(particle);
                }
            }

            createNeuralNetwork() {
                const container = document.getElementById('neuralNetwork');
                const nodeCount = 30;

                // Create neural nodes
                for (let i = 0; i < nodeCount; i++) {
                    const node = document.createElement('div');
                    node.className = 'neural-node';

                    const x = Math.random() * window.innerWidth;
                    const y = Math.random() * window.innerHeight;

                    node.style.left = x + 'px';
                    node.style.top = y + 'px';
                    node.style.animationDelay = Math.random() * 2 + 's';

                    container.appendChild(node);
                    this.neuralNodes.push({ element: node, x, y });
                }

                // Create connections between nearby nodes
                this.createNeuralConnections(container);
            }

            createNeuralConnections(container) {
                for (let i = 0; i < this.neuralNodes.length; i++) {
                    for (let j = i + 1; j < this.neuralNodes.length; j++) {
                        const node1 = this.neuralNodes[i];
                        const node2 = this.neuralNodes[j];

                        const distance = Math.sqrt(
                            Math.pow(node2.x - node1.x, 2) +
                            Math.pow(node2.y - node1.y, 2)
                        );

                        if (distance < 200) {
                            const connection = document.createElement('div');
                            connection.className = 'neural-connection';

                            const angle = Math.atan2(node2.y - node1.y, node2.x - node1.x) * 180 / Math.PI;

                            connection.style.width = distance + 'px';
                            connection.style.left = node1.x + 'px';
                            connection.style.top = node1.y + 'px';
                            connection.style.transform = `rotate(${angle}deg)`;
                            connection.style.animationDelay = Math.random() * 3 + 's';

                            container.appendChild(connection);
                        }
                    }
                }
            }

            createMatrixRain() {
                const container = document.getElementById('matrixRain');
                const characters = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';

                for (let i = 0; i < 50; i++) {
                    const column = document.createElement('div');
                    column.className = 'matrix-column';

                    let text = '';
                    for (let j = 0; j < 20; j++) {
                        text += characters[Math.floor(Math.random() * characters.length)] + '<br>';
                    }

                    column.innerHTML = text;
                    column.style.left = Math.random() * 100 + '%';
                    column.style.animationDuration = (Math.random() * 3 + 2) + 's';
                    column.style.animationDelay = Math.random() * 2 + 's';

                    container.appendChild(column);
                }
            }

            setupAudioVisualizer() {
                const container = document.getElementById('audioViz');

                for (let i = 0; i < 64; i++) {
                    const bar = document.createElement('div');
                    bar.className = 'audio-bar';
                    bar.style.setProperty('--height', Math.random() * 50 + 10 + 'px');
                    bar.style.animationDelay = Math.random() * 0.5 + 's';
                    container.appendChild(bar);
                }

                // Simulate audio data
                setInterval(() => {
                    const bars = container.children;
                    for (let bar of bars) {
                        bar.style.setProperty('--height', Math.random() * 50 + 10 + 'px');
                    }
                }, 100);
            }

            setupInteractivity() {
                // Custom cursor with trails
                const cursor = document.getElementById('cursor');
                const trails = [];

                // Create cursor trails
                for (let i = 0; i < 10; i++) {
                    const trail = document.createElement('div');
                    trail.className = 'cursor-trail';
                    document.body.appendChild(trail);
                    trails.push(trail);
                }

                document.addEventListener('mousemove', (e) => {
                    this.mouseX = (e.clientX / window.innerWidth) * 2 - 1;
                    this.mouseY = -(e.clientY / window.innerHeight) * 2 + 1;

                    cursor.style.left = e.clientX + 'px';
                    cursor.style.top = e.clientY + 'px';

                    // Update cursor trails
                    trails.forEach((trail, index) => {
                        setTimeout(() => {
                            trail.style.left = e.clientX + 'px';
                            trail.style.top = e.clientY + 'px';
                            trail.style.opacity = (10 - index) / 10;
                        }, index * 20);
                    });
                });

                // Scroll-based effects
                window.addEventListener('scroll', () => {
                    const scrollProgress = window.scrollY / (document.body.scrollHeight - window.innerHeight);

                    // Update glitch intensity
                    const glitchOverlay = document.getElementById('glitchOverlay');
                    glitchOverlay.style.opacity = scrollProgress * 0.3;

                    // Update 3D scene
                    if (this.morphingTorus) {
                        this.morphingTorus.material.uniforms.scroll.value = scrollProgress;
                    }
                });

                // Click effects
                document.addEventListener('click', (e) => {
                    this.createClickExplosion(e.clientX, e.clientY);
                });
            }

            createClickExplosion(x, y) {
                for (let i = 0; i < 20; i++) {
                    const particle = document.createElement('div');
                    particle.style.position = 'fixed';
                    particle.style.left = x + 'px';
                    particle.style.top = y + 'px';
                    particle.style.width = '4px';
                    particle.style.height = '4px';
                    particle.style.background = '#00ffff';
                    particle.style.borderRadius = '50%';
                    particle.style.pointerEvents = 'none';
                    particle.style.zIndex = '10000';

                    const angle = (i / 20) * Math.PI * 2;
                    const velocity = Math.random() * 100 + 50;

                    document.body.appendChild(particle);

                    particle.animate([
                        { transform: 'translate(0, 0) scale(1)', opacity: 1 },
                        {
                            transform: `translate(${Math.cos(angle) * velocity}px, ${Math.sin(angle) * velocity}px) scale(0)`,
                            opacity: 0
                        }
                    ], {
                        duration: 1000,
                        easing: 'ease-out'
                    }).onfinish = () => particle.remove();
                }
            }

            animate() {
                this.time += 0.01;

                // Update 3D scene
                if (this.particleSystem) {
                    this.particleSystem.material.uniforms.time.value = this.time;
                    this.particleSystem.material.uniforms.mouse.value.set(this.mouseX, this.mouseY);
                    this.particleSystem.rotation.y += 0.001;
                }

                if (this.morphingTorus) {
                    this.morphingTorus.material.uniforms.time.value = this.time;
                    this.morphingTorus.rotation.x += 0.005;
                    this.morphingTorus.rotation.y += 0.003;
                }

                // Update quantum particles
                this.particles.forEach(particle => {
                    const physics = particle.physics;

                    physics.x += physics.vx;
                    physics.y += physics.vy;
                    physics.life--;

                    // Boundary collision
                    if (physics.x < 0 || physics.x > window.innerWidth) physics.vx *= -1;
                    if (physics.y < 0 || physics.y > window.innerHeight) physics.vy *= -1;

                    // Mouse attraction
                    const mouseX = this.mouseX * window.innerWidth / 2 + window.innerWidth / 2;
                    const mouseY = -this.mouseY * window.innerHeight / 2 + window.innerHeight / 2;
                    const dx = mouseX - physics.x;
                    const dy = mouseY - physics.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < 100) {
                        physics.vx += dx * 0.0001;
                        physics.vy += dy * 0.0001;
                    }

                    particle.style.left = physics.x + 'px';
                    particle.style.top = physics.y + 'px';
                    particle.style.opacity = physics.life / 100;

                    // Respawn particle
                    if (physics.life <= 0) {
                        physics.x = Math.random() * window.innerWidth;
                        physics.y = Math.random() * window.innerHeight;
                        physics.vx = (Math.random() - 0.5) * 2;
                        physics.vy = (Math.random() - 0.5) * 2;
                        physics.life = Math.random() * 100 + 50;
                    }
                });

                this.renderer.render(this.scene, this.camera);
                requestAnimationFrame(() => this.animate());
            }
        }

        // Initialize the mind-blowing system
        document.addEventListener('DOMContentLoaded', () => {
            new MindBlowingTechSystem();
            console.log('🚀 MIND-BLOWING TECHNOLOGY SYSTEM ACTIVATED 🚀');
        });
    </script>
</body>
</html>
